@extends('layouts.app')

@section('title', 'Request Details')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <!-- Header Section -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                            <h1 class="text-3xl font-bold text-gray-900">Request Details</h1>
                        </div>
                        <div class="hidden sm:block">
                            <nav class="flex space-x-2 text-sm text-gray-500">
                                <a href="{{ route('dashboard.index') }}" class="hover:text-blue-600 transition-colors">Dashboard</a>
                                <span>/</span>
                                <a href="{{ route('requests.index') }}" class="hover:text-blue-600 transition-colors">Requests</a>
                                <span>/</span>
                                <span class="text-gray-900">Request #{{ $request_model->id }}</span>
                            </nav>
                        </div>
                    </div>

                    <!-- Status Badge -->
                    <div class="flex items-center space-x-3">
                        @php
                            $statusConfig = [
                                'pending' => ['bg-yellow-100', 'text-yellow-800', 'border-yellow-200', 'fas fa-clock'],
                                'approved' => ['bg-green-100', 'text-green-800', 'border-green-200', 'fas fa-check-circle'],
                                'rejected' => ['bg-red-100', 'text-red-800', 'border-red-200', 'fas fa-times-circle'],
                            ];
                            $config = $statusConfig[$request_model->status] ?? ['bg-gray-100', 'text-gray-800', 'border-gray-200', 'fas fa-question-circle'];
                        @endphp
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border {{ $config[0] }} {{ $config[1] }} {{ $config[2] }} {{ $request_model->status === 'pending' ? 'status-pending' : '' }}">
                            <i class="{{ $config[3] }} mr-2"></i>
                            {{ ucfirst($request_model->status) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        @if(session('error'))
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
                <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">
                                <strong>Error:</strong> {{ session('error') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Request Details Card -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                        <!-- Card Header -->
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                            <h2 class="text-xl font-semibold text-white flex items-center">
                                <i class="fas fa-info-circle mr-3"></i>
                                Request Information
                            </h2>
                        </div>

                        <!-- Card Body -->
                        <div class="p-6 space-y-6">
                            <!-- Request ID and Type -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-hashtag text-blue-600 text-lg"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Request ID</p>
                                            <p class="text-lg font-semibold text-gray-900">#{{ $request_model->id }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-tag text-purple-600 text-lg"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Request Type</p>
                                            <p class="text-lg font-semibold text-gray-900">{{ ucwords(str_replace('_', ' ', $request_model->type)) }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Church and Requester -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-church text-green-600 text-lg"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Church</p>
                                            <p class="text-lg font-semibold text-gray-900">{{ $request_model->church ? $request_model->church->name : 'Church not found' }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-user text-indigo-600 text-lg"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500">Requested By</p>
                                            <p class="text-lg font-semibold text-gray-900">{{ $request_model->user ? $request_model->user->full_name : 'User not found' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Timestamps -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-clock mr-2 text-gray-600"></i>
                                    Timeline
                                </h3>
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-plus text-blue-600 text-sm"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Request Created</p>
                                            <p class="text-sm text-gray-500">{{ $request_model->created_at ? $request_model->created_at->format('F j, Y \a\t g:i A') : 'Unknown' }}</p>
                                        </div>
                                    </div>

                                    @if ($request_model->approved_by)
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 {{ $request_model->status === 'approved' ? 'bg-green-100' : 'bg-red-100' }} rounded-full flex items-center justify-center">
                                                    <i class="fas {{ $request_model->status === 'approved' ? 'fa-check text-green-600' : 'fa-times text-red-600' }} text-sm"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">{{ $request_model->status === 'approved' ? 'Approved' : 'Rejected' }} by {{ $request_model->approver ? $request_model->approver->full_name : 'Unknown' }}</p>
                                                <p class="text-sm text-gray-500">{{ $request_model->approved_at ? $request_model->approved_at->format('F j, Y \a\t g:i A') : 'Unknown' }}</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Additional Details -->
                            @if (isset($request_model->details['description']) || ($request_model->status == 'rejected' && isset($request_model->details['rejection_reason'])))
                                <div class="border-t border-gray-200 pt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                        <i class="fas fa-file-text mr-2 text-gray-600"></i>
                                        Additional Information
                                    </h3>

                                    @if (isset($request_model->details['description']))
                                        <div class="bg-blue-50 rounded-lg p-4 mb-4">
                                            <h4 class="text-sm font-medium text-blue-900 mb-2">Description</h4>
                                            <p class="text-sm text-blue-800">{{ $request_model->details['description'] }}</p>
                                        </div>
                                    @endif

                                    @if ($request_model->status == 'rejected' && isset($request_model->details['rejection_reason']))
                                        <div class="bg-red-50 rounded-lg p-4">
                                            <h4 class="text-sm font-medium text-red-900 mb-2">Rejection Reason</h4>
                                            <p class="text-sm text-red-800">{{ $request_model->details['rejection_reason'] }}</p>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                        <!-- Card Header -->
                        <div class="bg-gradient-to-r from-gray-600 to-gray-700 px-6 py-4">
                            <h2 class="text-xl font-semibold text-white flex items-center">
                                <i class="fas fa-cogs mr-3"></i>
                                Actions
                            </h2>
                        </div>

                        <!-- Card Body -->
                        <div class="p-6 space-y-4">
                            <!-- Back Button -->
                            <a href="{{ route('requests.index') }}"
                               class="w-full inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Requests
                            </a>

                            @if($request_model->status === 'pending')
                                @can('approve-requests')
                                    @if($request_model->canBeApprovedBy(auth()->user()))
                                        <!-- Approve Button -->
                                        <form action="{{ route('requests.approve', $request_model) }}"
                                              method="POST"
                                              class="w-full"
                                              onsubmit="return confirm('Are you sure you want to approve this request?')">
                                            @csrf
                                            <button type="submit"
                                                    class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 shadow-md hover:shadow-lg">
                                                <i class="fas fa-check mr-2"></i>
                                                Approve Request
                                            </button>
                                        </form>

                                        <!-- Reject Button -->
                                        <button type="button"
                                                onclick="showRejectModal()"
                                                class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 shadow-md hover:shadow-lg">
                                            <i class="fas fa-times mr-2"></i>
                                            Reject Request
                                        </button>
                                    @endif
                                @endcan

                                @if($request_model->user_id === auth()->id() && auth()->user()->hasPermissionTo('manage-requests'))
                                    <a href="{{ route('requests.edit', $request_model) }}"
                                       class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-md hover:shadow-lg">
                                        <i class="fas fa-edit mr-2"></i>
                                        Edit Request
                                    </a>
                                @endif
                            @endif

                            <!-- Quick Info Section -->
                            <div class="border-t border-gray-200 pt-4 mt-6">
                                <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Info</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-500">Request ID</span>
                                        <span class="font-medium text-gray-900">#{{ $request_model->id }}</span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-500">Status</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $config[0] }} {{ $config[1] }}">
                                            {{ ucfirst($request_model->status) }}
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-500">Created</span>
                                        <span class="font-medium text-gray-900">{{ $request_model->created_at ? $request_model->created_at->format('M j, Y') : 'Unknown' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Reject Modal -->
        <div id="rejectModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
            <div class="relative bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto transform transition-all">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-white">Reject Request</h3>
                        </div>
                        <button type="button" onclick="hideRejectModal()" class="text-red-200 hover:text-white transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
                            <i class="fas fa-info-circle text-red-600"></i>
                            <p class="text-sm text-red-800">
                                You are about to reject request #{{ $request_model->id }}. Please provide a clear reason for rejection.
                            </p>
                        </div>
                    </div>

                    <form action="{{ route('requests.reject', $request_model) }}" method="POST">
                        @csrf
                        <div class="mb-6">
                            <label for="rejection_reason" class="block text-sm font-medium text-gray-700 mb-3">
                                <i class="fas fa-comment-alt mr-2 text-gray-500"></i>
                                Rejection Reason <span class="text-red-500">*</span>
                            </label>
                            <textarea id="rejection_reason"
                                      name="rejection_reason"
                                      rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none transition-all duration-200"
                                      placeholder="Please provide a detailed reason for rejecting this request. This will help the requester understand the decision and make necessary improvements for future requests."
                                      required></textarea>
                            <p class="mt-2 text-xs text-gray-500">
                                <i class="fas fa-lightbulb mr-1"></i>
                                Be specific and constructive in your feedback.
                            </p>
                        </div>

                        <div class="flex items-center justify-end space-x-3">
                            <button type="button"
                                    onclick="hideRejectModal()"
                                    class="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 font-medium shadow-md hover:shadow-lg">
                                <i class="fas fa-times mr-2"></i>
                                Reject Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showRejectModal() {
            const modal = document.getElementById('rejectModal');
            modal.classList.remove('hidden');

            // Focus on textarea for better UX
            setTimeout(() => {
                document.getElementById('rejection_reason').focus();
            }, 100);

            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        }

        function hideRejectModal() {
            const modal = document.getElementById('rejectModal');
            modal.classList.add('hidden');
            document.getElementById('rejection_reason').value = '';

            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('rejectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRejectModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideRejectModal();
            }
        });

        // Add smooth animations and better UX
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to action buttons
            const actionButtons = document.querySelectorAll('button[type="submit"], form button');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.type === 'submit') {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                        this.disabled = true;

                        // Re-enable after 3 seconds as fallback
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }, 3000);
                    }
                });
            });
        });
    </script>

    @push('styles')
    <style>
        /* Custom animations for the modal */
        #rejectModal {
            backdrop-filter: blur(4px);
        }

        #rejectModal > div {
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Smooth hover effects */
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Status badge pulse animation for pending status */
        .status-pending {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
        }
    </style>
    @endpush
@endsection
@extends('layouts.app')

@section('title', 'Create New User')
@section('page-title', 'Create New User')

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('users.index') }}" class="hover:text-gray-700">Users</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Create</span>
    </li>
@endsection

@section('page-actions')
    <a href="{{ route('users.index') }}"
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Users
    </a>
@endsection

@section('content')
<div class="max-w-2xl mx-auto">
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">User Information</h3>
            <p class="mt-1 text-sm text-gray-600">Create a new user account. The user will complete their profile details on first login.</p>
        </div>

        <form method="POST" action="{{ route('users.store') }}" class="p-6 space-y-6">
            @csrf

            <!-- Email -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input type="email"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-300 @enderror"
                       placeholder="Enter email address">
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Phone Number -->
            <div>
                <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                </label>
                <input type="tel"
                       id="phone_number"
                       name="phone_number"
                       value="{{ old('phone_number') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('phone_number') border-red-300 @enderror"
                       placeholder="Enter phone number (e.g., 255787504956)">
                @error('phone_number')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Church Assignment -->
            <div>
                <label for="church_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Church Assignment <span class="text-red-500">*</span>
                </label>
                <select id="church_id"
                        name="church_id"
                        required
                        onchange="filterRolesByChurch()"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('church_id') border-red-300 @enderror">
                    <option value="">Select a church</option>
                    @foreach($churches as $church)
                        <option value="{{ $church->id }}"
                                data-level="{{ $church->level->value }}"
                                {{ old('church_id') == $church->id ? 'selected' : '' }}>
                            {{ $church->name }} ({{ $church->level->value }})
                        </option>
                    @endforeach
                </select>
                @error('church_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Role Assignment -->
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                    Role Assignment <span class="text-red-500">*</span>
                </label>
                <select id="role"
                        name="role"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @error('role') border-red-300 @enderror">
                    <option value="">Select a role</option>
                    @foreach($roles as $role)
                        <option value="{{ $role->name }}"
                                data-level="{{ $role->level ?? 'all' }}"
                                {{ old('role') == $role->name ? 'selected' : '' }}>
                            {{ $role->name }}
                        </option>
                    @endforeach
                </select>
                @error('role')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Account Creation Process
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p><strong>What happens after creating the user:</strong></p>
                            <ul class="list-disc list-inside mt-1 space-y-1">
                                <li>A temporary password will be generated automatically</li>
                                <li>Login credentials (email, password, and OTP) will be sent via email and SMS</li>
                                <li>User must verify OTP on first login</li>
                                <li>User will be prompted to complete their profile and change password</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3 pt-6 border-t">
                <a href="{{ route('users.index') }}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Create User
                </button>
            </div>
        </form>

<script>
    // Role mapping for each church level
    const levelRoles = {
        'National': ['Archbishop', 'General Secretary', 'National Treasurer', 'National IT', 'National HR'],
        'Diocese': ['Bishop', 'Diocese Secretary', 'Diocese Treasurer', 'Diocese IT'],
        'Local': ['Pastor', 'Local Secretary', 'Local Treasurer', 'Local IT'],
        'Parish': ['Parish Pastor', 'Parish Secretary', 'Parish Treasurer', 'Parish IT'],
        'Branch': ['Branch Pastor', 'Branch Secretary', 'Branch Treasurer', 'Branch IT']
    };

    function filterRolesByChurch() {
        const churchSelect = document.getElementById('church_id');
        const roleSelect = document.getElementById('role');
        const selectedOption = churchSelect.options[churchSelect.selectedIndex];

        if (!selectedOption || !selectedOption.value) {
            // Show all roles if no church selected
            Array.from(roleSelect.options).forEach(option => {
                if (option.value !== '') {
                    option.style.display = 'block';
                }
            });
            return;
        }

        const churchLevel = selectedOption.getAttribute('data-level');
        const allowedRoles = levelRoles[churchLevel] || [];

        // Hide/show role options based on church level
        Array.from(roleSelect.options).forEach(option => {
            if (option.value === '') {
                option.style.display = 'block'; // Always show "Select a role" option
                return;
            }

            if (allowedRoles.includes(option.value)) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
                // Clear selection if currently selected role is not allowed
                if (option.selected) {
                    roleSelect.value = '';
                }
            }
        });
    }

    // Filter roles on page load if church is already selected
    document.addEventListener('DOMContentLoaded', function() {
        filterRolesByChurch();
    });
</script>

@endsection
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('churches', function (Blueprint $table) {
            $table->string('phone_number')->nullable()->after('location');
            $table->text('address')->nullable()->after('phone_number');
            $table->string('email')->nullable()->after('address');
            $table->string('district')->nullable()->after('email');
            $table->string('region')->nullable()->after('district'); // Mkoa
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('churches', function (Blueprint $table) {
            $table->dropColumn([
                'phone_number',
                'address',
                'email',
                'district',
                'region'
            ]);
        });
    }
};

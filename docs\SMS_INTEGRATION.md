# Huduma SMS Integration for FPCT System

## Overview

The FPCT System has been integrated with Huduma SMS service to provide reliable SMS functionality for Tanzania and other African countries. This replaces the previous Africa's Talking integration with a more robust and feature-rich SMS service.

## Features

- ✅ Send individual SMS messages
- ✅ Send bulk SMS messages
- ✅ OTP SMS for verification
- ✅ Welcome SMS for new users
- ✅ Announcement SMS
- ✅ Phone number validation for Tanzania
- ✅ SMS delivery reports
- ✅ Account balance checking
- ✅ Connection testing

## Configuration

### 1. Environment Variables

Add the following to your `.env` file:

```env
# Huduma SMS Configuration
HUDUMASMS_API_TOKEN=your_api_token_here
HUDUMASMS_SENDER_ID=FPCT
HUDUMASMS_BASE_URL=https://sms-api.huduma.cloud/api/v3
HUDUMASMS_CALLBACK_URL=https://your-domain.com/api/sms/callback
```

### 2. Getting Huduma SMS Credentials

1. Visit [Huduma Developers Portal](https://developers.huduma.cloud)
2. Create a HudumaCloud account if you don't have one
3. Register your application on the developer portal
4. Generate your API keys after approval
5. Get your **API Token** and configure your **Sender ID**

### 3. Phone Number Format

The system automatically formats phone numbers for Tanzania:
- Input: `**********` → Output: `************` (for Huduma SMS API)
- Input: `*********` → Output: `************` (for Huduma SMS API)
- Input: `************` → Output: `************` (already correct)
- Input: `+************` → Output: `************` (+ sign removed for API)

Note: Huduma SMS API requires phone numbers in international format without the + sign.

## Usage

### Testing SMS Service

Use the built-in test command:

```bash
# Test with default message
php artisan sms:test +************

# Test with custom message
php artisan sms:test +************ --message="Hello from FPCT!"
```

### Programmatic Usage

```php
use App\Services\HudumaSMSService;

// Inject the service
public function __construct(HudumaSMSService $smsService)
{
    $this->smsService = $smsService;
}

// Send a simple SMS
$success = $this->smsService->sendSMS('+************', 'Hello World!');

// Send OTP
$success = $this->smsService->sendOTP('+************', '123456', 'FPCT');

// Send welcome SMS
$success = $this->smsService->sendWelcomeSMS(
    '+************', 
    'John Doe', 
    'tempPass123', 
    '123456'
);

// Send bulk SMS
$phoneNumbers = ['+************', '+255787654321'];
$results = $this->smsService->sendBulkSMS($phoneNumbers, 'Bulk message');

// Send transaction SMS (automatically called by system)
$success = $this->smsService->sendTransactionSMS(
    '+************',
    'Revenue Collection',
    50000.00,
    'TZS',
    'St. John Branch',
    'Dar es Salaam Parish',
    'Monthly revenue collection',
    'TXN-ABC123-**************',
    'REF-ABC123-********'
);

// Check balance
$balance = $this->smsService->getBalance();

// Test connection
$isConnected = $this->smsService->testConnection();
```

## SMS Types in FPCT System

### 1. Welcome SMS
Sent when a new user is registered:
```
Welcome to FPCT System, John Doe! Your temporary password is: FPCT12345678. Your OTP is: 123456. Please login and change your password immediately.
```

### 2. OTP SMS
Sent for verification:
```
Your FPCT verification code is: 123456. This code will expire in 30 minutes. Do not share this code with anyone.
```

### 3. Password Reset SMS
Sent when password is reset:
```
Your FPCT password has been reset. New password: FPCT87654321. OTP: 654321. Please login and change your password.
```

### 4. Announcement SMS
Sent for church announcements:
```
FPCT Announcement: Sunday Service

Join us this Sunday at 9:00 AM for our special service...
```

### 5. Account Activation SMS
```
Your FPCT System account has been activated. You can now login to the system.
```

### 6. Transaction Completion SMS
Sent automatically for every successful transaction:
```
FPCT Transaction Completed!
Type: Revenue Collection
Amount: TZS 50,000.00
From: St. John Branch Church
To: Dar es Salaam Parish Church
Purpose: Monthly revenue collection
Ref: REF-ABC123-********
ID: TXN-XYZ789-**************
```

**Features:**
- Automatic SMS for all completed transactions
- Contains complete transaction details (source, destination, amount, purpose)
- Includes reference numbers for tracking
- Sent to transaction initiator immediately upon completion

## Error Handling

The SMS service includes comprehensive error handling:

- **Connection errors**: Logged and handled gracefully
- **Invalid phone numbers**: Validated before sending
- **API errors**: Logged with detailed error messages
- **Rate limiting**: Built into Huduma SMS service

## Monitoring and Logging

All SMS activities are logged:

```php
// Success logs
Log::info("SMS sent successfully to +************");

// Error logs
Log::error("Failed to send SMS to +************: Invalid phone number");
```

## Pricing

Tanzania SMS pricing varies based on your Huduma SMS plan:
- **Standard SMS**: Check your Huduma SMS dashboard for current rates
- **Bulk SMS**: Volume discounts available

Check current pricing at [Huduma SMS](https://hudumasms.com/pricing) or contact support

## Troubleshooting

### Common Issues

1. **SMS not sending**
   - Check API credentials
   - Verify account balance
   - Check phone number format

2. **Invalid phone number**
   - Ensure number is in valid Tanzania format (+255XXXXXXXXX)
   - Use the validation method: `$smsService->isValidPhoneNumber($phone)`

3. **Connection failed**
   - Verify internet connection
   - Check API token and sender ID
   - Test with: `php artisan sms:test +************`

### Debug Commands

```bash
# Test SMS service
php artisan sms:test +************

# Check logs
tail -f storage/logs/laravel.log | grep SMS

# Clear config cache
php artisan config:clear
```

## Security Considerations

1. **API Token Protection**: Never expose API tokens in client-side code
2. **Rate Limiting**: Huduma SMS has built-in rate limiting
3. **Phone Number Validation**: Always validate before sending
4. **Message Content**: Sanitize message content to prevent injection

## Migration from Africa's Talking

The system has been completely migrated from Africa's Talking to Huduma SMS:

- ✅ All Africa's Talking references removed
- ✅ Huduma SMS API integrated
- ✅ Phone number formatting updated for Huduma SMS API
- ✅ Error handling adapted for new API responses
- ✅ Test commands updated
- ✅ HTTP client (Guzzle) integrated for API calls

## Support

For Huduma SMS support:
- Documentation: https://apidocs.hudumasms.com/
- Developer Portal: https://developers.huduma.cloud/
- Support: Contact through HudumaCloud platform

For FPCT System SMS issues:
- Check application logs
- Use the test command: `php artisan sms:test +************`
- Verify configuration in .env file

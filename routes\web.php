<?php

use App\Http\Controllers\ChurchController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\RequestController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SessionController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    // Redirect financial users to financial dashboard
    if ($user->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer'])) {
        return redirect()->route('financial.dashboard');
    }

    return redirect()->route('dashboard.index');
});

// Language switching routes (available to all users)
Route::post('language/switch', [LanguageController::class, 'switch'])->name('language.switch');
Route::get('language/current', [LanguageController::class, 'current'])->name('language.current');
Route::get('language/list', [LanguageController::class, 'getLanguages'])->name('language.list');

// Session management routes (for authenticated users)
Route::middleware('auth')->group(function () {
    Route::get('session/check', [SessionController::class, 'check'])->name('session.check');
    Route::post('session/extend', [SessionController::class, 'extend'])->name('session.extend');
    Route::get('session/config', [SessionController::class, 'config'])->name('session.config');
});

// Test route for session timeout (remove in production)
Route::middleware('auth')->get('session/test', function () {
    return view('session-test');
})->name('session.test');

Route::get('login', [UserController::class, 'showLoginForm'])->name('login');
Route::post('login', [UserController::class, 'login']);
Route::post('logout', [UserController::class, 'logout'])->name('logout');
Route::get('register', [UserController::class, 'showRegisterForm'])->name('register');
Route::post('register', [UserController::class, 'register']);

Route::middleware('auth')->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

    Route::resource('users', UserController::class);
    Route::get('profile', [UserController::class, 'profile'])->name('user.profile');
    Route::put('profile', [UserController::class, 'updateProfile'])->name('user.profile.update');
    Route::put('profile/password', [UserController::class, 'updatePassword'])->name('user.password.update');
    Route::post('profile/password/verify', [UserController::class, 'verifyCurrentPassword'])->name('user.password.verify');
    Route::get('first-login', [UserController::class, 'showFirstLogin'])->name('user.first-login');
    Route::post('first-login', [UserController::class, 'verifyFirstLogin'])->name('user.first-login.verify');

    // Church specific routes (must be before resource routes)
    Route::get('churches/trashed', [ChurchController::class, 'trashed'])->name('churches.trashed');
    Route::post('churches/{id}/restore', [ChurchController::class, 'restore'])->name('churches.restore');
    Route::delete('churches/{id}/force-delete', [ChurchController::class, 'forceDelete'])->name('churches.force-delete');

    // Hierarchical navigation routes
    Route::get('churches/hierarchical-data', [ChurchController::class, 'getHierarchicalData'])->name('churches.hierarchical-data');
    Route::get('churches/dioceses', [ChurchController::class, 'getDioceses'])->name('churches.dioceses');

    // Church resource routes
    Route::resource('churches', ChurchController::class);

    // Church Reports
    Route::get('church-reports', [App\Http\Controllers\ChurchReportController::class, 'index'])->name('church-reports.index');
    Route::post('church-reports/generate', [App\Http\Controllers\ChurchReportController::class, 'generateLevelReport'])->name('church-reports.generate');
    Route::get('church-reports/generate', function() {
        return redirect()->route('church-reports.index')->with('error', 'Please use the form to generate reports.');
    });
    Route::get('church-reports/statistics', [App\Http\Controllers\ChurchReportController::class, 'getStatistics'])->name('church-reports.statistics');

    Route::resource('requests', RequestController::class);
    Route::post('requests/{request}/approve', [RequestController::class, 'approve'])->name('requests.approve');
    Route::post('requests/{request}/reject', [RequestController::class, 'reject'])->name('requests.reject');

    Route::resource('messages', MessageController::class)->only(['index', 'create', 'store', 'show']);
    Route::post('messages/group', [MessageController::class, 'sendGroupMessage'])->name('messages.group');
    Route::post('messages/announcement', [MessageController::class, 'sendAnnouncement'])->name('messages.announcement');

    // Role and Permission Management (Super Admin only)
    Route::middleware(['permission:manage-permissions'])->group(function () {
        Route::resource('roles', RoleController::class);
        Route::post('roles/{role}/assign-user', [RoleController::class, 'assignUser'])->name('roles.assign-user');
        Route::delete('roles/{role}/remove-user', [RoleController::class, 'removeUser'])->name('roles.remove-user');

        Route::resource('permissions', PermissionController::class);
    });
});

// Password reset routes
Route::get('password/request', [UserController::class, 'showPasswordRequestForm'])->name('password.request');
Route::post('password/request', [UserController::class, 'requestPasswordReset'])->name('password.request.submit');
Route::get('password/reset', [UserController::class, 'showPasswordResetForm'])->name('password.reset');
Route::post('password/reset', [UserController::class, 'resetPassword'])->name('password.reset.submit');

// Financial Management Routes
Route::middleware(['auth'])->group(function () {
    // Financial Dashboard
    Route::get('/financial/dashboard', [App\Http\Controllers\FinancialDashboardController::class, 'index'])->name('financial.dashboard');
    Route::get('/financial/summary', [App\Http\Controllers\FinancialDashboardController::class, 'getSummary'])->name('financial.summary');

    // Transactions
    Route::resource('transactions', App\Http\Controllers\TransactionController::class);
    Route::post('/transactions/{transaction}/approve', [App\Http\Controllers\TransactionController::class, 'approve'])->name('transactions.approve');
    Route::post('/transactions/{transaction}/reject', [App\Http\Controllers\TransactionController::class, 'reject'])->name('transactions.reject');
    Route::post('/transactions/{transaction}/cancel', [App\Http\Controllers\TransactionController::class, 'cancel'])->name('transactions.cancel');
    Route::get('/transactions/statistics', [App\Http\Controllers\TransactionController::class, 'getStatistics'])->name('transactions.statistics');

    // Contributions
    Route::resource('contributions', App\Http\Controllers\ContributionController::class);
    Route::patch('/contributions/{contribution}/status', [App\Http\Controllers\ContributionController::class, 'updateStatus'])->name('contributions.update-status');

    // Financial Reports
    Route::resource('financial-reports', App\Http\Controllers\FinancialReportController::class);
    Route::get('/financial-reports/{financialReport}/pdf', [App\Http\Controllers\FinancialReportController::class, 'downloadPdf'])->name('financial-reports.pdf');
    Route::get('/financial-reports/{financialReport}/excel', [App\Http\Controllers\FinancialReportController::class, 'downloadExcel'])->name('financial-reports.excel');
    Route::post('/financial-reports/quick/monthly', [App\Http\Controllers\FinancialReportController::class, 'quickMonthly'])->name('financial-reports.quick-monthly');
    Route::post('/financial-reports/quick/quarterly', [App\Http\Controllers\FinancialReportController::class, 'quickQuarterly'])->name('financial-reports.quick-quarterly');
    Route::post('/financial-reports/quick/annual', [App\Http\Controllers\FinancialReportController::class, 'quickAnnual'])->name('financial-reports.quick-annual');
    Route::get('/financial-reports/{financialReport}/data', [App\Http\Controllers\FinancialReportController::class, 'getReportData'])->name('financial-reports.data');

    // Receipts
    Route::resource('receipts', App\Http\Controllers\ReceiptController::class);
    Route::get('/receipts/{receipt}/pdf', [App\Http\Controllers\ReceiptController::class, 'downloadPdf'])->name('receipts.pdf');
    Route::post('/receipts/{receipt}/email', [App\Http\Controllers\ReceiptController::class, 'email'])->name('receipts.email');
    Route::post('/receipts/bulk/generate', [App\Http\Controllers\ReceiptController::class, 'generateBulk'])->name('receipts.bulk-generate');
    Route::get('/receipts/{receipt}/preview', [App\Http\Controllers\ReceiptController::class, 'preview'])->name('receipts.preview');
    Route::post('/transactions/{transaction}/receipt', [App\Http\Controllers\ReceiptController::class, 'generateForTransaction'])->name('transactions.generate-receipt');

    // Test route for intended redirect functionality
    Route::get('/test-redirect', function () {
        return response()->json([
            'message' => 'This is a protected route that requires authentication',
            'current_url' => request()->fullUrl(),
            'user' => auth()->user() ? auth()->user()->email : 'Not authenticated'
        ]);
    })->name('test.redirect');
});

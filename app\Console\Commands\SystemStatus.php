<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Church;
use App\Models\Request;
use App\Models\Message;
use App\Services\HudumaSMSService;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SystemStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check FPCT system status and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏢 FPCT System Status Check');
        $this->info('================================');

        // Database Status
        $this->checkDatabase();

        // Users Status
        $this->checkUsers();

        // Churches Status
        $this->checkChurches();

        // Roles & Permissions
        $this->checkRolesAndPermissions();

        // SMS Service
        $this->checkSMSService();

        // System Configuration
        $this->checkConfiguration();

        $this->info('================================');
        $this->info('✅ System status check complete!');
    }

    private function checkDatabase(): void
    {
        $this->info("\n📊 Database Status:");

        try {
            $userCount = User::count();
            $churchCount = Church::count();
            $requestCount = Request::count();
            $messageCount = Message::count();

            $this->info("  ✓ Users: {$userCount}");
            $this->info("  ✓ Churches: {$churchCount}");
            $this->info("  ✓ Requests: {$requestCount}");
            $this->info("  ✓ Messages: {$messageCount}");
        } catch (\Exception $e) {
            $this->error("  ✗ Database connection failed: " . $e->getMessage());
        }
    }

    private function checkUsers(): void
    {
        $this->info("\n👥 Users Status:");

        $activeUsers = User::where('is_active', true)->count();
        $inactiveUsers = User::where('is_active', false)->count();
        $firstLoginUsers = User::where('is_first_login', true)->count();

        $this->info("  ✓ Active users: {$activeUsers}");
        $this->info("  ✓ Inactive users: {$inactiveUsers}");
        $this->info("  ✓ First login pending: {$firstLoginUsers}");

        // Check super admin
        $superAdmin = User::where('email', '<EMAIL>')->first();
        if ($superAdmin) {
            $this->info("  ✓ Super admin exists: {$superAdmin->full_name}");
        } else {
            $this->warn("  ⚠ Super admin not found");
        }
    }

    private function checkChurches(): void
    {
        $this->info("\n⛪ Churches Status:");

        $nationalCount = Church::where('level', 'National')->count();
        $regionalCount = Church::where('level', 'Regional')->count();
        $localCount = Church::where('level', 'Local')->count();
        $parishCount = Church::where('level', 'Parish')->count();
        $branchCount = Church::where('level', 'Branch')->count();

        $this->info("  ✓ National: {$nationalCount}");
        $this->info("  ✓ Regional: {$regionalCount}");
        $this->info("  ✓ Local: {$localCount}");
        $this->info("  ✓ Parish: {$parishCount}");
        $this->info("  ✓ Branch: {$branchCount}");
    }

    private function checkRolesAndPermissions(): void
    {
        $this->info("\n🔐 Roles & Permissions:");

        $roleCount = Role::count();
        $permissionCount = Permission::count();

        $this->info("  ✓ Roles: {$roleCount}");
        $this->info("  ✓ Permissions: {$permissionCount}");

        // Check key roles
        $keyRoles = ['Archbishop', 'Bishop', 'Pastor', 'Parish Pastor', 'Branch Pastor'];
        foreach ($keyRoles as $role) {
            $exists = Role::where('name', $role)->exists();
            $status = $exists ? '✓' : '✗';
            $this->info("  {$status} {$role}: " . ($exists ? 'exists' : 'missing'));
        }
    }

    private function checkSMSService(): void
    {
        $this->info("\n📱 SMS Service (Huduma SMS):");

        $apiToken = config('services.hudumasms.api_token');
        $senderId = config('services.hudumasms.sender_id');

        if ($apiToken && $senderId && $apiToken !== 'your_api_token_here') {
            $this->info("  ✓ Credentials configured");

            try {
                $smsService = app(HudumaSMSService::class);
                $connected = $smsService->testConnection();

                if ($connected) {
                    $this->info("  ✓ Connection successful");
                    $balance = $smsService->getBalance();
                    if ($balance) {
                        $this->info("  ✓ Account balance: {$balance} credits");
                    }
                } else {
                    $this->warn("  ⚠ Connection failed");
                }
            } catch (\Exception $e) {
                $this->error("  ✗ SMS service error: " . $e->getMessage());
            }
        } else {
            $this->warn("  ⚠ SMS credentials not configured");
            $this->info("    Update HUDUMASMS_API_TOKEN and HUDUMASMS_SENDER_ID in .env");
        }
    }

    private function checkConfiguration(): void
    {
        $this->info("\n⚙️ Configuration:");

        $appEnv = config('app.env');
        $appDebug = config('app.debug') ? 'enabled' : 'disabled';
        $dbConnection = config('database.default');
        $mailDriver = config('mail.default');
        $broadcastDriver = config('broadcasting.default');

        $this->info("  ✓ Environment: {$appEnv}");
        $this->info("  ✓ Debug: {$appDebug}");
        $this->info("  ✓ Database: {$dbConnection}");
        $this->info("  ✓ Mail: {$mailDriver}");
        $this->info("  ✓ Broadcasting: {$broadcastDriver}");

        // Check important environment variables
        $envVars = [
            'APP_URL' => config('app.url'),
            'DB_DATABASE' => config('database.connections.pgsql.database'),
            'MAIL_FROM_ADDRESS' => config('mail.from.address'),
        ];

        foreach ($envVars as $key => $value) {
            $status = $value ? '✓' : '⚠';
            $displayValue = $value ?: 'not set';
            $this->info("  {$status} {$key}: {$displayValue}");
        }
    }
}

@extends('layouts.app')

@section('title', __('churches.edit_church'))
@section('page-title', __('churches.edit_church'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.index') }}" class="hover:text-gray-700">{{ __('churches.churches') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.show', $church) }}" class="hover:text-gray-700">{{ $church->name }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ __('common.edit') }}</span>
    </li>
@endsection

@section('page-actions')
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <x-language-switcher position="bottom-right" size="normal" />

        <a href="{{ route('churches.show', $church) }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-eye mr-2"></i>
            {{ __('common.view') }}
        </a>
        <a href="{{ route('churches.index') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ __('common.back') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600">{{ __('common.language') }}:</span>
                    <div class="flex space-x-1">
                        @foreach(config('app.locale_names') as $locale => $info)
                            <button onclick="switchLanguage('{{ $locale }}')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100' }}">
                                {{ $info['flag'] }} {{ $info['native'] }}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <!-- Church Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <div class="h-16 w-16 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-church text-white text-2xl"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">{{ __('churches.editing_church') }}: {{ $church->name }}</h1>
                        <p class="text-green-100">{{ __('common.' . strtolower($church->level->value)) }} - {{ $church->location }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.total_members') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</p>
                        <p class="text-xs text-gray-500">{{ $stats['active_users'] }} {{ __('common.active') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-crown text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.leaders') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_leaders'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.leadership_positions') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-sitemap text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.child_churches') }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['child_churches'] }}</p>
                        <p class="text-xs text-gray-500">{{ __('churches.under_supervision') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ __('churches.established') }}</p>
                        <p class="text-lg font-bold text-gray-900">
                            {{ $church->date_established ? $church->date_established->format('M Y') : __('common.unknown') }}
                        </p>
                        <p class="text-xs text-gray-500">
                            {{ $church->date_established ? $church->date_established->diffForHumans() : '' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('churches.update', $church) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        {{ __('churches.basic_information') }}
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_name') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-church text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ old('name', $church->name) }}"
                                       required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('name') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_church_name') }}">
                            </div>
                            @error('name')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="level" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_level') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-layer-group text-gray-400"></i>
                                </div>
                                <select name="level"
                                        id="level"
                                        required
                                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('level') border-red-300 @enderror">
                                    <option value="">{{ __('churches.select_level') }}</option>
                                    @foreach ($levels as $level)
                                        <option value="{{ $level }}" {{ old('level', $church->level->value) == $level ? 'selected' : '' }}>
                                            {{ __('common.' . strtolower($level)) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('level')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.location') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="location"
                                       name="location"
                                       value="{{ old('location', $church->location) }}"
                                       required
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('location') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_location') }}">
                            </div>
                            @error('location')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.phone_number') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-phone text-gray-400"></i>
                                </div>
                                <input type="tel"
                                       id="phone_number"
                                       name="phone_number"
                                       value="{{ old('phone_number', $church->phone_number) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('phone_number') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_phone_number') }}">
                            </div>
                            @error('phone_number')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.email') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ old('email', $church->email) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('email') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_email') }}">
                            </div>
                            @error('email')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.address') }}
                            </label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 pointer-events-none">
                                    <i class="fas fa-map-marked-alt text-gray-400"></i>
                                </div>
                                <textarea id="address"
                                          name="address"
                                          rows="3"
                                          class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('address') border-red-300 @enderror"
                                          placeholder="{{ __('churches.enter_address') }}">{{ old('address', $church->address) }}</textarea>
                            </div>
                            @error('address')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="district" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.district') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="district"
                                       name="district"
                                       value="{{ old('district', $church->district) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('district') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_district') }}">
                            </div>
                            @error('district')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="region" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.region') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-globe-africa text-gray-400"></i>
                                </div>
                                <input type="text"
                                       id="region"
                                       name="region"
                                       value="{{ old('region', $church->region) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('region') border-red-300 @enderror"
                                       placeholder="{{ __('churches.enter_region') }}">
                            </div>
                            @error('region')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div>
                            <label for="date_established" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.date_established') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar text-gray-400"></i>
                                </div>
                                <input type="date"
                                       id="date_established"
                                       name="date_established"
                                       value="{{ old('date_established', $church->date_established?->format('Y-m-d')) }}"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('date_established') border-red-300 @enderror">
                            </div>
                            @error('date_established')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="parent_church_id" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.parent_church') }}
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                                <select name="parent_church_id"
                                        id="parent_church_id"
                                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 @error('parent_church_id') border-red-300 @enderror">
                                    <option value="">{{ __('churches.no_parent_church') }}</option>
                                    @foreach ($parentChurches as $parent)
                                        <option value="{{ $parent->id }}" {{ old('parent_church_id', $church->parent_church_id) == $parent->id ? 'selected' : '' }}>
                                            {{ $parent->name }} ({{ __('common.' . strtolower($parent->level->value)) }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @error('parent_church_id')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leaders Management -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-crown mr-2 text-green-600"></i>
                            {{ __('churches.church_leaders') }}
                            <span id="leader-count" class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {{ count($currentLeaders) }}
                            </span>
                        </h2>
                        <button type="button"
                                onclick="addLeaderRow()"
                                class="inline-flex items-center px-3 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('churches.add_leader') }}
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="leaders-container" class="space-y-4">
                        @forelse ($currentLeaders as $index => $leader)
                            <div class="leader-row bg-gray-50 rounded-lg p-4 border border-gray-200" data-index="{{ $index }}">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('churches.select_leader') }}</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user text-gray-400"></i>
                                            </div>
                                            <select name="leaders[{{ $index }}][user_id]"
                                                    class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200">
                                                <option value="">{{ __('churches.choose_user') }}</option>
                                                @foreach ($users as $user)
                                                    <option value="{{ $user->id }}" {{ $leader->user_id == $user->id ? 'selected' : '' }}
                                                            @if(isset($churchUsers) && $churchUsers->contains($user)) data-church-member="true" @endif>
                                                        {{ $user->full_name }}
                                                        @if(isset($churchUsers) && $churchUsers->contains($user))
                                                            ({{ __('churches.church_member') }})
                                                        @endif
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('churches.position') }}</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user-tag text-gray-400"></i>
                                            </div>
                                            <input type="text"
                                                   name="leaders[{{ $index }}][position]"
                                                   value="{{ $leader->position }}"
                                                   placeholder="{{ __('churches.enter_position') }}"
                                                   class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200">
                                            <button type="button"
                                                    onclick="removeLeaderRow(this)"
                                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-red-400 hover:text-red-600">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                                <i class="fas fa-crown text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500 text-lg">{{ __('churches.no_leaders_assigned') }}</p>
                                <p class="text-gray-400 text-sm mb-4">{{ __('churches.click_add_leader_to_start') }}</p>
                                <button type="button"
                                        onclick="addLeaderRow()"
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                    <i class="fas fa-plus mr-2"></i>
                                    {{ __('churches.add_first_leader') }}
                                </button>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('churches.show', $church) }}"
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        {{ __('common.cancel') }}
                    </a>
                    <button type="submit"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        {{ __('churches.update_church') }}
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        let leaderIndex = {{ count($currentLeaders) }};

        function addLeaderRow() {
            const container = document.getElementById('leaders-container');
            const emptyState = container.querySelector('.text-center.py-8');
            if (emptyState) {
                emptyState.remove();
            }

            const leaderRow = document.createElement('div');
            leaderRow.className = 'leader-row bg-gray-50 rounded-lg p-4 border border-gray-200';
            leaderRow.setAttribute('data-index', leaderIndex);
            leaderRow.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('churches.select_leader') }}</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                            <select name="leaders[${leaderIndex}][user_id]"
                                    class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200">
                                <option value="">{{ __('churches.choose_user') }}</option>
                                @foreach ($users as $user)
                                    <option value="{{ $user->id }}"
                                            @if(isset($churchUsers) && $churchUsers->contains($user)) data-church-member="true" @endif>
                                        {{ $user->full_name }}
                                        @if(isset($churchUsers) && $churchUsers->contains($user))
                                            ({{ __('churches.church_member') }})
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('churches.position') }}</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user-tag text-gray-400"></i>
                            </div>
                            <input type="text"
                                   name="leaders[${leaderIndex}][position]"
                                   placeholder="{{ __('churches.enter_position') }}"
                                   class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200">
                            <button type="button"
                                    onclick="removeLeaderRow(this)"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-red-400 hover:text-red-600">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(leaderRow);
            leaderIndex++;
            updateLeaderCount();
        }

        function removeLeaderRow(button) {
            const row = button.closest('.leader-row');
            row.remove();
            updateLeaderCount();

            // Show empty state if no leaders
            const container = document.getElementById('leaders-container');
            if (container.children.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                        <i class="fas fa-crown text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg">{{ __('churches.no_leaders_assigned') }}</p>
                        <p class="text-gray-400 text-sm mb-4">{{ __('churches.click_add_leader_to_start') }}</p>
                        <button type="button"
                                onclick="addLeaderRow()"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('churches.add_first_leader') }}
                        </button>
                    </div>
                `;
            }
        }

        function updateLeaderCount() {
            const count = document.querySelectorAll('.leader-row').length;
            document.getElementById('leader-count').textContent = count;
        }
    </script>
@endsection
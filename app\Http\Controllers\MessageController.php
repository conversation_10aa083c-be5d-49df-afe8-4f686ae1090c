<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use App\Models\Message;
use App\Models\User;
use App\Models\Church;
use App\Notifications\MessageNotification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class MessageController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
        $this->middleware('auth');
        $this->middleware('permission:send-messages', ['only' => ['create', 'store', 'sendGroupMessage', 'sendAnnouncement']]);
        $this->middleware('permission:view-messages', ['only' => ['index', 'show']]);
    }

    // List all messages
    public function index(Request $request)
    {
        $user = Auth::user();

        // Apply filters
        $query = Message::query();

        // Filter by user's messages (sent or received)
        $query->where(function ($q) use ($user) {
            $q->where('sender_id', $user->id)
              ->orWhereHas('recipients', function ($subQ) use ($user) {
                  $subQ->where('user_id', $user->id);
              });
        });

        // Apply search filter
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('content', 'like', "%{$request->search}%")
                  ->orWhereHas('sender', function ($subQ) use ($request) {
                      $subQ->where('full_name', 'like', "%{$request->search}%");
                  });
            });
        }

        // Apply message type filters
        if ($request->filter) {
            switch ($request->filter) {
                case 'unread':
                    $query->whereHas('recipients', function ($q) use ($user) {
                        $q->where('user_id', $user->id)->whereNull('read_at');
                    });
                    break;
                case 'sent':
                    $query->where('sender_id', $user->id);
                    break;
                case 'announcements':
                    $query->where('is_announcement', true);
                    break;
            }
        }

        // Load relationships and paginate
        $messages = $query->with([
                'sender:id,full_name,email',
                'recipients' => function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                },
                'church:id,name'
            ])
            ->withCount('recipients')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Mark received messages as read (only for messages where user is recipient)
        $messagesToMarkRead = [];
        foreach ($messages as $message) {
            // Check if user is a recipient and message is unread
            $userRecipient = $message->recipients->where('id', $user->id)->first();
            if ($userRecipient && !$userRecipient->pivot->read_at) {
                $messagesToMarkRead[] = $message->id;
            }
        }

        // Bulk update read status
        if (!empty($messagesToMarkRead)) {
            DB::table('message_recipient')
                ->whereIn('message_id', $messagesToMarkRead)
                ->where('user_id', $user->id)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
        }

        return view('messages.index', compact('messages'));
    }

    // Show create message form
    public function create()
    {
        $user = Auth::user();
        $users = User::where('id', '!=', Auth::user()->id)->get();
        $churches = Church::all();

        // Get available roles for role-based messaging - only show specific roles
        $displayRoles = ['IT', 'BISHOP', 'PASTOR', 'TREASURER', 'SECRETARY'];
        $roles = collect($displayRoles)->map(function($roleName) {
            return (object) ['name' => $roleName];
        });

        // Get church levels for level-based messaging
        $churchLevels = ['National', 'Regional', 'Local', 'Parish', 'Branch'];

        // Get role statistics for better UX
        $roleStats = $this->getRoleStatistics();

        return view('messages.create', compact('users', 'churches', 'roles', 'churchLevels', 'roleStats'));
    }

    // Store new message
    public function store(Request $request)
    {
        try {
            $request->validate([
                'recipient_type' => 'required|in:individual,church,role,level,role_at_level',
                'recipient_ids' => 'required_if:recipient_type,individual|array',
                'recipient_ids.*' => 'exists:users,id',
                'church_id' => 'required_if:recipient_type,church|exists:churches,id',
                'role_name' => 'required_if:recipient_type,role,role_at_level|string',
                'church_level' => 'required_if:recipient_type,level,role_at_level|string',
                'level_church_id' => 'nullable|exists:churches,id',
                'content' => 'required|string|max:5000',
                'is_group_message' => 'boolean',
                'is_announcement' => 'boolean',
            ]);

            $user = Auth::user();

            // Get recipients based on type first to check if any exist
            $recipients = $this->getRecipientsByType($request);

            if (empty($recipients)) {
                return redirect()->back()
                    ->withInput()
                    ->with('toast', [
                        'type' => 'error',
                        'message' => 'No recipients found for the selected criteria. Please check your selection.'
                    ]);
            }

            $message = Message::create([
                'sender_id' => $user->id,
                'content' => $request->content,
                'church_id' => $request->church_id ?? $user->church_id,
                'is_group_message' => $request->boolean('is_group_message', false),
                'is_announcement' => $request->boolean('is_announcement', false),
            ]);

            $message->recipients()->attach($recipients);

            // Broadcast real-time message
            broadcast(new MessageSent($message))->toOthers();

            // Send notifications for offline users
            $this->sendNotifications($message);

            // Determine message type for better feedback
            $messageType = $request->boolean('is_announcement') ? 'announcement' :
                          ($request->boolean('is_group_message') ? 'group message' : 'message');

            return redirect()->route('messages.index')
                ->with('success', ucfirst($messageType) . ' sent successfully to ' . count($recipients) . ' recipient(s).')
                ->with('toast', [
                    'type' => 'success',
                    'message' => '✓ ' . ucfirst($messageType) . ' delivered to ' . count($recipients) . ' recipient(s)'
                ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('toast', [
                    'type' => 'error',
                    'message' => 'Please check the form for errors and try again.'
                ]);
        } catch (\Exception $e) {
            Log::error('Message sending failed: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('toast', [
                    'type' => 'error',
                    'message' => 'Failed to send message. Please try again.'
                ]);
        }
    }

    // Show message details
    public function show(Message $message)
    {
        $this->authorizeMessageAccess($message);

        // Load the message with proper relationships
        $message->load([
            'sender:id,full_name,email',
            'recipients:id,full_name,email',
            'church:id,name'
        ]);

        // Mark as read if user is a recipient
        $user = Auth::user();
        if ($message->recipients->contains($user->id)) {
            $userRecipient = $message->recipients->where('id', $user->id)->first();
            if ($userRecipient && !$userRecipient->pivot->read_at) {
                $message->recipients()->updateExistingPivot($user->id, ['read_at' => now()]);
                // Reload the relationship to get updated pivot data
                $message->load('recipients:id,full_name,email');
            }
        }

        return view('messages.show', compact('message'));
    }

    // Send group message
    public function sendGroupMessage(Request $request)
    {
        $request->validate([
            'church_id' => 'required|exists:churches,id',
            'content' => 'required|string|max:5000',
        ]);

        $user = Auth::user();
        $message = Message::create([
            'sender_id' => $user->id,
            'content' => $request->content,
            'church_id' => $request->church_id,
            'is_group_message' => true,
            'is_announcement' => false,
        ]);

        // Attach all users in the church as recipients
        $recipients = User::where('church_id', $request->church_id)->pluck('id');
        $message->recipients()->attach($recipients);

        // Broadcast real-time message
        broadcast(new MessageSent($message))->toOthers();

        // Send notifications for offline users
        $this->sendNotifications($message);

        return redirect()->route('messages.index')->with('success', 'Group message sent successfully.');
    }

    // Send announcement (bulk SMS/email)
    public function sendAnnouncement(Request $request)
    {
        $request->validate([
            'church_id' => 'required|exists:churches,id',
            'content' => 'required|string|max:5000',
        ]);

        $user = Auth::user();
        $message = Message::create([
            'sender_id' => $user->id,
            'content' => $request->content,
            'church_id' => $request->church_id,
            'is_group_message' => true,
            'is_announcement' => true,
        ]);

        // Attach all users in the church hierarchy as recipients
        $church = Church::find($request->church_id);
        $recipient_ids = $this->getChurchHierarchyUsers($church);
        $message->recipients()->attach($recipient_ids);

        // Broadcast real-time message
        broadcast(new MessageSent($message))->toOthers();

        // Send announcement notifications with SMS
        $this->sendAnnouncementNotifications($message, $recipient_ids);

        return redirect()->route('messages.index')
            ->with('success', 'Announcement sent successfully to ' . count($recipient_ids) . ' recipient(s).')
            ->with('toast', [
                'type' => 'success',
                'message' => '📢 Announcement delivered to ' . count($recipient_ids) . ' recipient(s) via SMS and email'
            ]);
    }

    // Helper method to get all users in church hierarchy
    private function getChurchHierarchyUsers(Church $church)
    {
        $recipient_ids = User::where('church_id', $church->id)->pluck('id')->toArray();
        $child_churches = Church::where('parent_church_id', $church->id)->get();
        foreach ($child_churches as $child) {
            $recipient_ids = array_merge($recipient_ids, $this->getChurchHierarchyUsers($child));
        }
        return array_unique($recipient_ids);
    }

    // Helper method to send notifications for offline users
    private function sendNotifications(Message $message)
    {
        $recipients = User::whereIn('id', $message->recipients->pluck('id'))
            ->get();

        // Send notifications to all recipients (they can check if they're online)
        Notification::send($recipients, new MessageNotification($message));
    }

    // Helper method for bulk SMS/email notifications
    private function sendBulkNotifications(Message $message, array $recipient_ids)
    {
        $recipients = User::whereIn('id', $recipient_ids)->get();
        Notification::send($recipients, new MessageNotification($message));
    }

    // Helper method for announcement notifications with SMS
    private function sendAnnouncementNotifications(Message $message, array $recipient_ids)
    {
        $recipients = User::whereIn('id', $recipient_ids)->get();
        $sender = User::find($message->sender_id); // Get the sender user object

        // Send bulk announcement with SMS support
        $this->notificationService->sendBulkAnnouncement(
            'FPCT Announcement',
            $message->content,
            $recipients->all(), // Get array of User objects
            $sender
        );
    }

    // Helper method to get recipients based on type
    private function getRecipientsByType(Request $request): array
    {
        switch ($request->recipient_type) {
            case 'individual':
                return $request->recipient_ids ?? [];

            case 'church':
                return User::where('church_id', $request->church_id)
                    ->where('is_active', true)
                    ->pluck('id')
                    ->toArray();

            case 'role':
                // Send to all users with roles that match the selected role pattern (fuzzy matching)
                $matchingRoles = $this->getFuzzyMatchingRoles($request->role_name);
                return User::whereHas('roles', function ($query) use ($matchingRoles) {
                        $query->whereIn('name', $matchingRoles);
                    })
                    ->where('is_active', true)
                    ->when($request->level_church_id, function ($query) use ($request) {
                        // If specific church is selected, get users from that church and its descendants
                        $church = Church::find($request->level_church_id);
                        $churchIds = collect([$church->id])
                            ->merge($church->getAllDescendants()->pluck('id'))
                            ->toArray();
                        return $query->whereIn('church_id', $churchIds);
                    })
                    ->pluck('id')
                    ->toArray();

            case 'role_at_level':
                // Send to users with roles that match the selected role pattern at specific church level
                $matchingRoles = $this->getFuzzyMatchingRoles($request->role_name);
                return User::whereHas('roles', function ($query) use ($matchingRoles) {
                        $query->whereIn('name', $matchingRoles);
                    })
                    ->whereHas('church', function ($q) use ($request) {
                        $q->where('level', $request->church_level);
                    })
                    ->where('is_active', true)
                    ->when($request->level_church_id, function ($query) use ($request) {
                        // If specific church is selected, filter by that church's hierarchy
                        $church = Church::find($request->level_church_id);
                        $churchIds = collect([$church->id])
                            ->merge($church->getAllDescendants()->pluck('id'))
                            ->toArray();
                        return $query->whereIn('church_id', $churchIds);
                    })
                    ->pluck('id')
                    ->toArray();

            case 'level':
                // Send to all users at specific church level (regardless of role)
                $query = User::whereHas('church', function ($q) use ($request) {
                    $q->where('level', $request->church_level);
                })->where('is_active', true);

                // If specific church is selected, filter by that church's hierarchy
                if ($request->level_church_id) {
                    $church = Church::find($request->level_church_id);
                    $churchIds = collect([$church->id])
                        ->merge($church->getAllDescendants()->pluck('id'))
                        ->toArray();
                    $query->whereIn('church_id', $churchIds);
                }

                return $query->pluck('id')->toArray();

            default:
                return [];
        }
    }

    // Helper method to get role statistics for display roles with fuzzy matching
    private function getRoleStatistics(): array
    {
        $displayRoles = ['IT', 'BISHOP', 'PASTOR', 'TREASURER', 'SECRETARY'];
        $stats = [];

        foreach ($displayRoles as $displayRole) {
            $matchingRoles = $this->getFuzzyMatchingRoles($displayRole);
            $totalUsers = 0;
            $levelBreakdown = [];

            // Aggregate statistics for all matching roles
            foreach ($matchingRoles as $roleName) {
                $roleUsers = User::whereHas('roles', function ($query) use ($roleName) {
                    $query->where('name', $roleName);
                })->where('is_active', true)->count();

                $totalUsers += $roleUsers;

                foreach (['National', 'Regional', 'Local', 'Parish', 'Branch'] as $level) {
                    $count = User::whereHas('roles', function ($query) use ($roleName) {
                            $query->where('name', $roleName);
                        })
                        ->whereHas('church', function ($q) use ($level) {
                            $q->where('level', $level);
                        })
                        ->where('is_active', true)
                        ->count();

                    if ($count > 0) {
                        $levelBreakdown[$level] = ($levelBreakdown[$level] ?? 0) + $count;
                    }
                }
            }

            $stats[$displayRole] = [
                'total' => $totalUsers,
                'levels' => $levelBreakdown
            ];
        }

        return $stats;
    }

    // Helper method to get roles that fuzzy match the selected role
    private function getFuzzyMatchingRoles(string $selectedRole): array
    {
        // Get all roles from database
        $allRoles = \Spatie\Permission\Models\Role::pluck('name')->toArray();

        // Define fuzzy matching patterns for each display role
        $rolePatterns = [
            'IT' => ['IT'],
            'BISHOP' => ['Bishop'],
            'PASTOR' => ['Pastor'],
            'TREASURER' => ['Treasurer'],
            'SECRETARY' => ['Secretary']
        ];

        $selectedRoleUpper = strtoupper($selectedRole);

        if (!isset($rolePatterns[$selectedRoleUpper])) {
            return [];
        }

        $patterns = $rolePatterns[$selectedRoleUpper];
        $matchingRoles = [];

        foreach ($allRoles as $role) {
            foreach ($patterns as $pattern) {
                if (stripos($role, $pattern) !== false) {
                    $matchingRoles[] = $role;
                    break; // Found a match, no need to check other patterns for this role
                }
            }
        }

        return $matchingRoles;
    }

    // Helper method to authorize message access
    private function authorizeMessageAccess(Message $message)
    {
        $user = Auth::user();
        if ($message->sender_id == $user->id || $message->recipients->contains($user->id) || $user->hasRole('Super Admin')) {
            return true;
        }
        abort(403, 'Unauthorized action.');
    }
}
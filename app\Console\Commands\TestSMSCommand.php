<?php

namespace App\Console\Commands;

use App\Services\HudumaSMSService;
use Illuminate\Console\Command;

class TestSMSCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sms:test {phone} {--message=Test message from FPCT System}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test SMS functionality with Huduma SMS';

    protected HudumaSMSService $smsService;

    public function __construct(HudumaSMSService $smsService)
    {
        parent::__construct();
        $this->smsService = $smsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phone = $this->argument('phone');
        $message = $this->option('message');

        $this->info('Testing SMS service...');
        $this->info("Phone: {$phone}");
        $this->info("Message: {$message}");

        // Test connection first
        if (!$this->smsService->testConnection()) {
            $this->error('Failed to connect to Huduma SMS service. Please check your credentials.');
            return 1;
        }

        $this->info('✓ Connection to Huduma SMS successful');

        // Validate phone number
        if (!$this->smsService->isValidPhoneNumber($phone)) {
            $this->error('Invalid phone number format. Please use Tanzania format (+255XXXXXXXXX)');
            return 1;
        }

        $this->info('✓ Phone number format is valid');

        // Get balance
        $balance = $this->smsService->getBalance();
        if ($balance) {
            $this->info("Account Balance: {$balance}");
        }

        // Send test SMS
        $this->info('Sending test SMS...');

        $success = $this->smsService->sendSMS($phone, $message);

        if ($success) {
            $this->info('✓ SMS sent successfully!');

            // Show pricing info
            $pricing = $this->smsService->getSMSPricing();
            $this->info("SMS Cost: ~{$pricing['local']['price_per_sms']} {$pricing['local']['currency']}");

            return 0;
        } else {
            $this->error('✗ Failed to send SMS. Check logs for details.');
            return 1;
        }
    }
}

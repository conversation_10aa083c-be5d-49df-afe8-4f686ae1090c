<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'FPCT System')</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    @vite(['resources/js/app.js'])

    <script>
        window.Laravel = {
            userId: {{ Auth::id() ?? 'null' }},
            csrfToken: '{{ csrf_token() }}'
        };
    </script>

    <!-- Prevent caching for authenticated pages -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    @stack('styles')
</head>

<body class="bg-gray-50 font-sans antialiased">
    @auth
        <div class="flex h-screen bg-gray-100" x-data="{ sidebarOpen: false }">
            <!-- Sidebar for desktop -->
            <div class="hidden lg:flex lg:flex-shrink-0">
                <div class="flex flex-col w-64">
                    <div class="flex flex-col flex-grow bg-gradient-to-b from-blue-600 to-blue-800 pt-5 pb-4 overflow-y-auto">
                        <!-- Logo -->
                        <div class="flex items-center flex-shrink-0 px-4">
                            @php
                                $homeRoute = (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Assistant Treasurer']))
                                    ? route('financial.dashboard')
                                    : route('dashboard.index');
                            @endphp
                            <a href="{{ $homeRoute }}" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT System
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="mt-8 flex-1 px-2 space-y-1">
                            <!-- Dashboard -->
                            @php
                                $dashboardRoute = (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']))
                                    ? route('financial.dashboard')
                                    : route('dashboard.index');
                                $isDashboardActive = request()->routeIs('dashboard.*') ||
                                    (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']) && request()->routeIs('financial.dashboard'));
                            @endphp
                            <a href="{{ $dashboardRoute }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ $isDashboardActive ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                                {{ __('common.dashboard') }}
                            </a>

                            @can('view-users')
                            <!-- Users -->
                            <a href="{{ route('users.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('users.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-users mr-3 text-lg"></i>
                                {{ __('common.users') }}
                            </a>
                            @endcan

                            @can('view-churches')
                            <!-- Churches -->
                            <a href="{{ route('churches.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('churches.index') || request()->routeIs('churches.show') || request()->routeIs('churches.create') || request()->routeIs('churches.edit') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-church mr-3 text-lg"></i>
                                {{ __('common.churches') }}
                            </a>

                            @can('view-church-reports')
                            <!-- Church Reports -->
                            <a href="{{ route('church-reports.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('church-reports.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-chart-bar mr-3 text-lg"></i>
                                {{ __('churches.church_reports') }}
                            </a>
                            @endcan
                            @endcan

                            @can('view-requests')
                            <!-- Requests -->
                            <a href="{{ route('requests.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative {{ request()->routeIs('requests.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-file-alt mr-3 text-lg"></i>
                                {{ __('common.requests') }}
                                @if(auth()->user() && auth()->user()->pendingApprovals()->count() > 0)
                                    <span class="ml-auto bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                        {{ auth()->user()->pendingApprovals()->count() }}
                                    </span>
                                @endif
                            </a>
                            @endcan

                            @can('view-messages')
                            <!-- Messages -->
                            <a href="{{ route('messages.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative {{ request()->routeIs('messages.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-envelope mr-3 text-lg"></i>
                                Messages
                                @if(auth()->user() && auth()->user()->unreadMessages()->count() > 0)
                                    <span class="ml-auto bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                        {{ auth()->user()->unreadMessages()->count() }}
                                    </span>
                                @endif
                            </a>
                            @endcan

                            @can('view-financial-dashboard')
                            @unless(auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']))
                            <!-- Financial Management -->
                            <a href="{{ route('financial.dashboard') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('financial.*', 'transactions.*', 'contributions.*', 'receipts.*', 'financial-reports.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-coins mr-3 text-lg"></i>
                                {{ __('Financial Management') }}
                            </a>
                            @endunless
                            @endcan

                            @can('manage-permissions')
                            <!-- Roles & Permissions -->
                            <a href="{{ route('roles.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('roles.*', 'permissions.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-user-shield mr-3 text-lg"></i>
                                Roles & Permissions
                            </a>
                            @endcan
                        </nav>

                        <!-- Language Selection Section -->
                        <div class="flex-shrink-0 border-t border-blue-700 p-4">
                            <div class="flex items-center justify-between">
                                <span class="text-xs font-medium text-blue-200 uppercase tracking-wider">{{ __('common.language') }}</span>
                                <div x-data="{ open: false }" class="relative">
                                    <button @click="open = !open"
                                            class="flex items-center space-x-2 px-3 py-2 rounded-md bg-blue-700 hover:bg-blue-600 transition-colors duration-200">
                                        <span class="text-lg">{{ config('app.locale_names')[app()->getLocale()]['flag'] ?? '🌐' }}</span>
                                        <span class="text-sm text-white">{{ config('app.locale_names')[app()->getLocale()]['native'] ?? 'Language' }}</span>
                                        <i class="fas fa-chevron-down text-xs text-blue-200"></i>
                                    </button>
                                    <div x-show="open"
                                         @click.away="open = false"
                                         x-transition:enter="transition ease-out duration-100"
                                         x-transition:enter-start="transform opacity-0 scale-95"
                                         x-transition:enter-end="transform opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-75"
                                         x-transition:leave-start="transform opacity-100 scale-100"
                                         x-transition:leave-end="transform opacity-0 scale-95"
                                         class="absolute bottom-full left-0 mb-2 w-56 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                                        @foreach(config('app.locale_names') as $locale => $info)
                                            <button onclick="switchLanguage('{{ $locale }}')"
                                                    class="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-50 text-blue-700' : '' }}">
                                                <span class="mr-3 text-lg">{{ $info['flag'] }}</span>
                                                <div class="flex flex-col items-start flex-1">
                                                    <span class="font-medium">{{ $info['native'] }}</span>
                                                    <span class="text-xs text-gray-500">{{ $info['name'] }}</span>
                                                </div>
                                                @if(app()->getLocale() === $locale)
                                                    <i class="fas fa-check text-blue-600"></i>
                                                @endif
                                            </button>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Profile Section -->
                        <div class="flex-shrink-0 flex border-t border-blue-700 p-4">
                            <div class="flex items-center w-full" x-data="{ open: false }">
                                <div class="flex-shrink-0">
                                    <img class="h-10 w-10 rounded-full object-cover"
                                         src="{{ Auth::user()?->profile_picture_url ?? '/images/default-avatar.png' }}"
                                         alt="{{ Auth::user()?->full_name ?? 'User' }}">
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-white">{{ Auth::user()?->full_name ?? 'User' }}</p>
                                    <p class="text-xs text-blue-200">{{ Auth::user()?->getRoleNames()->first() ?? 'User' }}</p>
                                </div>
                                <div class="relative">
                                    <button @click="open = !open" class="text-blue-200 hover:text-white">
                                        <i class="fas fa-chevron-up" x-show="open"></i>
                                        <i class="fas fa-chevron-down" x-show="!open"></i>
                                    </button>
                                    <div x-show="open" 
                                         @click.away="open = false"
                                         x-transition:enter="transition ease-out duration-100"
                                         x-transition:enter-start="transform opacity-0 scale-95"
                                         x-transition:enter-end="transform opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-75"
                                         x-transition:leave-start="transform opacity-100 scale-100"
                                         x-transition:leave-end="transform opacity-0 scale-95"
                                         class="absolute bottom-full right-0 mb-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                        <a href="{{ route('user.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-user mr-2"></i> {{ __('common.profile') }}
                                        </a>
                                        <form action="{{ route('logout') }}" method="POST">
                                            @csrf
                                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-sign-out-alt mr-2"></i> {{ __('common.logout') }}
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile sidebar -->
            <div x-show="sidebarOpen" class="fixed inset-0 flex z-40 lg:hidden">
                <div x-show="sidebarOpen" 
                     x-transition:enter="transition-opacity ease-linear duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="transition-opacity ease-linear duration-300"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 bg-gray-600 bg-opacity-75" 
                     @click="sidebarOpen = false"></div>

                <div x-show="sidebarOpen"
                     x-transition:enter="transition ease-in-out duration-300 transform"
                     x-transition:enter-start="-translate-x-full"
                     x-transition:enter-end="translate-x-0"
                     x-transition:leave="transition ease-in-out duration-300 transform"
                     x-transition:leave-start="translate-x-0"
                     x-transition:leave-end="-translate-x-full"
                     class="relative flex-1 flex flex-col max-w-xs w-full bg-gradient-to-b from-blue-600 to-blue-800">
                    
                    <div class="absolute top-0 right-0 -mr-12 pt-2">
                        <button @click="sidebarOpen = false" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                            <i class="fas fa-times text-white"></i>
                        </button>
                    </div>

                    <!-- Mobile navigation content (same as desktop) -->
                    <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                        <!-- Logo -->
                        <div class="flex items-center flex-shrink-0 px-4">
                            @php
                                $homeRoute = (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Assistant Treasurer']))
                                    ? route('financial.dashboard')
                                    : route('dashboard.index');
                            @endphp
                            <a href="{{ $homeRoute }}" class="flex items-center text-white font-bold text-xl">
                                <i class="fas fa-church mr-3"></i>
                                FPCT System
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="mt-8 px-2 space-y-1">
                            <!-- Dashboard -->
                            @php
                                $dashboardRoute = (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']))
                                    ? route('financial.dashboard')
                                    : route('dashboard.index');
                                $isDashboardActive = request()->routeIs('dashboard.*') ||
                                    (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']) && request()->routeIs('financial.dashboard'));
                            @endphp
                            <a href="{{ $dashboardRoute }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ $isDashboardActive ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                                Dashboard
                            </a>

                            @can('view-users')
                            <a href="{{ route('users.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('users.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-users mr-3 text-lg"></i>
                                Users
                            </a>
                            @endcan

                            @can('view-churches')
                            <a href="{{ route('churches.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('churches.index') || request()->routeIs('churches.show') || request()->routeIs('churches.create') || request()->routeIs('churches.edit') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-church mr-3 text-lg"></i>
                                Churches
                            </a>

                            @can('view-church-reports')
                            <!-- Church Reports -->
                            <a href="{{ route('church-reports.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('church-reports.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-chart-bar mr-3 text-lg"></i>
                                Church Reports
                            </a>
                            @endcan
                            @endcan

                            @can('view-requests')
                            <a href="{{ route('requests.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative {{ request()->routeIs('requests.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-file-alt mr-3 text-lg"></i>
                                Requests
                                @if(auth()->user() && auth()->user()->pendingApprovals()->count() > 0)
                                    <span class="ml-auto bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                        {{ auth()->user()->pendingApprovals()->count() }}
                                    </span>
                                @endif
                            </a>
                            @endcan

                            @can('view-messages')
                            <a href="{{ route('messages.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 relative {{ request()->routeIs('messages.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-envelope mr-3 text-lg"></i>
                                Messages
                                @if(auth()->user() && auth()->user()->unreadMessages()->count() > 0)
                                    <span class="ml-auto bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                        {{ auth()->user()->unreadMessages()->count() }}
                                    </span>
                                @endif
                            </a>
                            @endcan

                            @can('view-financial-dashboard')
                            @unless(auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'National Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Parish Treasurer', 'Branch Treasurer', 'Assistant Treasurer']))
                            <!-- Financial Management -->
                            <a href="{{ route('financial.dashboard') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('financial.*', 'transactions.*', 'contributions.*', 'receipts.*', 'financial-reports.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-coins mr-3 text-lg"></i>
                                {{ __('Financial Management') }}
                            </a>
                            @endunless
                            @endcan

                            @can('manage-permissions')
                            <a href="{{ route('roles.index') }}"
                               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 {{ request()->routeIs('roles.*', 'permissions.*') ? 'bg-blue-700 text-white' : 'text-blue-100 hover:bg-blue-700 hover:text-white' }}">
                                <i class="fas fa-user-shield mr-3 text-lg"></i>
                                Roles & Permissions
                            </a>
                            @endcan
                        </nav>
                    </div>

                    <!-- Mobile User Profile Section -->
                    <div class="flex-shrink-0 flex border-t border-blue-700 p-4">
                        <div class="flex items-center w-full">
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full object-cover"
                                     src="{{ Auth::user()?->profile_picture_url ?? '/images/default-avatar.png' }}"
                                     alt="{{ Auth::user()?->full_name ?? 'User' }}">
                            </div>
                            <div class="ml-3 flex-1">
                                <p class="text-sm font-medium text-white">{{ Auth::user()?->full_name ?? 'User' }}</p>
                                <p class="text-xs text-blue-200">{{ Auth::user()?->getRoleNames()->first() ?? 'User' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <div class="flex flex-col w-0 flex-1 overflow-hidden">
                <!-- Top navigation bar for mobile -->
                <div class="lg:hidden">
                    <div class="flex items-center justify-between bg-white shadow-sm px-4 py-2">
                        <button @click="sidebarOpen = true" class="text-gray-500 hover:text-gray-600">
                            <i class="fas fa-bars text-xl"></i>
                        </button>

                        <!-- Breadcrumb for mobile -->
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <i class="fas fa-home"></i>
                            <span>/</span>
                            <span class="font-medium">@yield('page-title', 'Dashboard')</span>
                        </div>

                        <!-- Language Dropdown (Mobile) -->
                        <div class="flex items-center mr-3" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="flex items-center space-x-1 px-2 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200">
                                <span class="text-lg">{{ config('app.locale_names')[app()->getLocale()]['flag'] ?? '🌐' }}</span>
                                <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                            </button>
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-16 top-12 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                                @foreach(config('app.locale_names') as $locale => $info)
                                    <button onclick="switchLanguage('{{ $locale }}')"
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 {{ app()->getLocale() === $locale ? 'bg-blue-50 text-blue-700' : '' }}">
                                        <span class="mr-3 text-lg">{{ $info['flag'] }}</span>
                                        <div class="flex flex-col items-start">
                                            <span class="font-medium">{{ $info['native'] }}</span>
                                            <span class="text-xs text-gray-500">{{ $info['name'] }}</span>
                                        </div>
                                        @if(app()->getLocale() === $locale)
                                            <i class="fas fa-check ml-auto text-blue-600"></i>
                                        @endif
                                    </button>
                                @endforeach
                            </div>
                        </div>

                        <!-- Mobile profile -->
                        <div class="flex items-center" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center">
                                <img class="h-8 w-8 rounded-full object-cover"
                                     src="{{ Auth::user()?->profile_picture_url ?? '/images/default-avatar.png' }}"
                                     alt="{{ Auth::user()?->full_name ?? 'User' }}">
                            </button>
                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-4 top-12 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="{{ route('user.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i> {{ __('common.profile') }}
                                </a>
                                <form action="{{ route('logout') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i> {{ __('common.logout') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <main class="flex-1 relative overflow-y-auto focus:outline-none">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <!-- Page header for desktop -->
                            <div class="hidden lg:block mb-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h1 class="text-2xl font-bold text-gray-900">@yield('page-title', 'Dashboard')</h1>
                                        <nav class="flex mt-2" aria-label="Breadcrumb">
                                            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                                                <li>
                                                    @php
                                                        $homeRoute = (auth()->user() && auth()->user()->hasAnyRole(['Treasurer', 'Regional Treasurer', 'Local Treasurer', 'Assistant Treasurer']))
                                                            ? route('financial.dashboard')
                                                            : route('dashboard.index');
                                                    @endphp
                                                    <a href="{{ $homeRoute }}" class="hover:text-gray-700">
                                                        <i class="fas fa-home"></i>
                                                    </a>
                                                </li>
                                                @hasSection('breadcrumbs')
                                                    @yield('breadcrumbs')
                                                @else
                                                    <li>
                                                        <span class="mx-2">/</span>
                                                        <span class="font-medium text-gray-900">@yield('page-title', 'Dashboard')</span>
                                                    </li>
                                                @endif
                                            </ol>
                                        </nav>
                                    </div>
                                    @hasSection('page-actions')
                                        <div class="flex space-x-3">
                                            @yield('page-actions')
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Main content -->
                            @yield('content')
                        </div>
                    </div>
                </main>
            </div>
        </div>
    @else
        <!-- Guest layout -->
        <div class="min-h-screen">
            @yield('content')
        </div>
    @endauth

    @stack('scripts')

    <!-- Language Switching Script -->
    <script>
        function switchLanguage(locale) {
            // Show loading state
            const loadingToast = showToast('{{ __("common.please_wait") }}...', 'info');

            // Make AJAX request to switch language
            fetch('{{ route("language.switch") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    locale: locale
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide loading toast
                    hideToast(loadingToast);

                    // Show success message
                    showToast(data.message || 'Language switched successfully', 'success');

                    // Reload page to apply new language
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                } else {
                    hideToast(loadingToast);
                    showToast(data.message || 'Failed to switch language', 'error');
                }
            })
            .catch(error => {
                hideToast(loadingToast);
                showToast('Error switching language', 'error');
                console.error('Language switch error:', error);
            });
        }

        // Toast notification system
        function showToast(message, type = 'info') {
            const toastId = 'toast-' + Date.now();
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-md shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center space-x-2">
                    <span>${message}</span>
                    <button onclick="hideToast('${toastId}')" class="ml-2 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Auto hide after 3 seconds (except for loading)
            if (type !== 'info') {
                setTimeout(() => {
                    hideToast(toastId);
                }, 3000);
            }

            return toastId;
        }

        function hideToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // Show toast notifications from session
        @if(session('toast'))
            document.addEventListener('DOMContentLoaded', function() {
                showToast('{{ session('toast.message') }}', '{{ session('toast.type') }}');
            });
        @endif
    </script>
</body>
</html>

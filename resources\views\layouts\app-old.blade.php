<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'FPCT System')</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    @vite(['resources/js/app.js'])

    <script>
        window.Laravel = {
            userId: {{ Auth::id() ?? 'null' }},
            csrfToken: '{{ csrf_token() }}'
        };
    </script>

    @stack('styles')
</head>

<body class="bg-gray-50 font-sans antialiased">
    <div class="min-h-screen">
        @auth
            <div class="flex h-screen bg-gray-100" x-data="{ sidebarOpen: false }">
                <!-- Sidebar for desktop -->
                <div class="hidden lg:flex lg:flex-shrink-0">
                    <div class="flex flex-col w-64">
                        <div class="flex flex-col flex-grow bg-gradient-to-b from-blue-600 to-blue-800 pt-5 pb-4 overflow-y-auto">
                            <!-- Logo -->
                            <div class="flex items-center flex-shrink-0 px-4">
                                <a href="{{ route('dashboard.index') }}" class="flex items-center text-white font-bold text-xl">
                                    <i class="fas fa-church mr-3"></i>
                                    FPCT System
                                </a>
                            </div>

                            <!-- Desktop Navigation -->
                            <div class="hidden md:ml-10 md:flex md:space-x-2">
                                <!-- Dashboard -->
                                <div class="relative group">
                                    <a href="{{ route('dashboard.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 {{ request()->routeIs('dashboard.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-tachometer-alt text-lg"></i>
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Dashboard
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>

                                @can('view-users')
                                <!-- Users -->
                                <div class="relative group">
                                    <a href="{{ route('users.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 {{ request()->routeIs('users.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-users text-lg"></i>
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Users
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>
                                @endcan

                                @can('view-churches')
                                <!-- Churches -->
                                <div class="relative group">
                                    <a href="{{ route('churches.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 {{ request()->routeIs('churches.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-church text-lg"></i>
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Churches
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>
                                @endcan

                                @can('view-requests')
                                <!-- Requests -->
                                <div class="relative group">
                                    <a href="{{ route('requests.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 relative {{ request()->routeIs('requests.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-file-alt text-lg"></i>
                                        @if(auth()->user()->pendingApprovals()->count() > 0)
                                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                                                {{ auth()->user()->pendingApprovals()->count() }}
                                            </span>
                                        @endif
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Requests
                                        @if(auth()->user()->pendingApprovals()->count() > 0)
                                            ({{ auth()->user()->pendingApprovals()->count() }} pending)
                                        @endif
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>
                                @endcan

                                @can('view-messages')
                                <!-- Messages -->
                                <div class="relative group">
                                    <a href="{{ route('messages.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 relative {{ request()->routeIs('messages.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-envelope text-lg"></i>
                                        @if(auth()->user()->unreadMessages()->count() > 0)
                                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                                                {{ auth()->user()->unreadMessages()->count() }}
                                            </span>
                                        @endif
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Messages
                                        @if(auth()->user()->unreadMessages()->count() > 0)
                                            ({{ auth()->user()->unreadMessages()->count() }} unread)
                                        @endif
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>
                                @endcan

                                @can('manage-permissions')
                                <!-- Roles & Permissions -->
                                <div class="relative group">
                                    <a href="{{ route('roles.index') }}"
                                       class="text-white hover:text-blue-200 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center w-10 h-10 {{ request()->routeIs('roles.*', 'permissions.*') ? 'bg-blue-700' : 'hover:bg-blue-700' }}">
                                        <i class="fas fa-user-shield text-lg"></i>
                                    </a>
                                    <!-- Tooltip -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                        Roles & Permissions
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                                    </div>
                                </div>
                                @endcan
                            </div>
                        </div>

                        <!-- User menu -->
                        <div class="flex items-center">
                            <!-- Church info -->
                            <div class="hidden md:block text-white text-sm mr-4">
                                <div class="font-medium">{{ Auth::user()->church->name }}</div>
                                <div class="text-blue-200 text-xs">{{ Auth::user()->church->level->value }} Level</div>
                            </div>

                            <!-- Profile dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open"
                                        class="flex items-center text-white hover:text-blue-200 focus:outline-none focus:text-blue-200 transition-colors duration-200">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                                            <span class="text-sm font-medium">{{ substr(Auth::user()->full_name, 0, 1) }}</span>
                                        </div>
                                        <span class="hidden md:block">{{ Auth::user()->full_name }}</span>
                                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                    </div>
                                </button>

                                <div x-show="open"
                                     @click.away="open = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">

                                    <a href="{{ route('user.profile') }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i> Profile
                                    </a>

                                    @if(Auth::user()->getRoleNames()->isNotEmpty())
                                    <div class="px-4 py-2 text-xs text-gray-500 border-t">
                                        Role: {{ Auth::user()->getRoleNames()->first() }}
                                    </div>
                                    @endif

                                    <form action="{{ route('logout') }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Mobile menu button -->
                            <button @click="mobileMenuOpen = !mobileMenuOpen"
                                    class="md:hidden ml-2 text-white hover:text-blue-200 focus:outline-none">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Navigation -->
                <div x-show="mobileMenuOpen"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="md:hidden bg-blue-700">
                    <div class="px-2 pt-2 pb-3 space-y-1">
                        <a href="{{ route('dashboard.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>

                        @can('view-users')
                        <a href="{{ route('users.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-users mr-2"></i> Users
                        </a>
                        @endcan

                        @can('view-churches')
                        <a href="{{ route('churches.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-church mr-2"></i> Churches
                        </a>
                        @endcan

                        @can('view-requests')
                        <a href="{{ route('requests.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-file-alt mr-2"></i> Requests
                        </a>
                        @endcan

                        @can('view-messages')
                        <a href="{{ route('messages.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-envelope mr-2"></i> Messages
                        </a>
                        @endcan

                        @can('manage-permissions')
                        <a href="{{ route('roles.index') }}"
                           class="block text-white hover:text-blue-200 px-3 py-2 rounded-md text-base font-medium">
                            <i class="fas fa-user-shield mr-2"></i> Roles & Permissions
                        </a>
                        @endcan
                    </div>
                </div>
            </nav>
        @endauth

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <i class="fas fa-times cursor-pointer" onclick="this.parentElement.parentElement.style.display='none'"></i>
                </span>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <i class="fas fa-times cursor-pointer" onclick="this.parentElement.parentElement.style.display='none'"></i>
                </span>
            </div>
        @endif

        @if(session('warning'))
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mx-4 mt-4" role="alert">
                <span class="block sm:inline">{{ session('warning') }}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <i class="fas fa-times cursor-pointer" onclick="this.parentElement.parentElement.style.display='none'"></i>
                </span>
            </div>
        @endif

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            @yield('content')
        </main>
    </div>

    @stack('scripts')
</body>

</html>
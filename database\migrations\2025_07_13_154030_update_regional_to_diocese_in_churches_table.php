<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update any existing churches with level 'Regional' to 'Diocese'
        DB::table('churches')
            ->where('level', 'Regional')
            ->update(['level' => 'Diocese']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert Diocese back to Regional
        DB::table('churches')
            ->where('level', 'Diocese')
            ->update(['level' => 'Regional']);
    }
};

@extends('layouts.app')

@section('title', __('churches.create_title'))
@section('page-title', __('churches.add_new_church'))

@section('breadcrumbs')
    <li>
        <span class="mx-2">/</span>
        <a href="{{ route('churches.index') }}" class="hover:text-gray-700">{{ __('churches.churches') }}</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">{{ __('churches.add') }}</span>
    </li>
@endsection

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900" x-text="getContextualTitle()">{{ __('churches.add_new_church') }}</h1>
                    <p class="mt-2 text-sm text-gray-600">{{ __('churches.add_church_description') }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('churches.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ __('churches.back_to_churches') }}
                    </a>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('churches.store') }}" class="space-y-8" x-data="churchForm()">
            @csrf

            <!-- Church Information Card -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-church mr-3 text-blue-600"></i>
                        {{ __('churches.church_information') }}
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">{{ __('churches.basic_church_details') }}</p>
                </div>

                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Church Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_name') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ old('name') }}"
                                       required
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('name') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_church_name') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-church text-gray-400"></i>
                                </div>
                            </div>
                            @error('name')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Church Level -->
                        <div>
                            <label for="level" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.church_level') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="level"
                                        id="level"
                                        required
                                        x-model="selectedLevel"
                                        @change="updateParentChurches()"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('level') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                                    <option value="">{{ __('churches.select_level') }}</option>
                                    @foreach ($levels as $level)
                                        <option value="{{ $level }}" {{ old('level') == $level ? 'selected' : '' }}>
                                            {{ __('churches.' . strtolower($level)) }}
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-layer-group text-gray-400"></i>
                                </div>
                            </div>
                            @error('level')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.location') }} <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="location"
                                       name="location"
                                       value="{{ old('location') }}"
                                       required
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('location') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_location') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                </div>
                            </div>
                            @error('location')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.phone_number') }}
                            </label>
                            <div class="relative">
                                <input type="tel"
                                       id="phone_number"
                                       name="phone_number"
                                       value="{{ old('phone_number') }}"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('phone_number') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_phone_number') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-phone text-gray-400"></i>
                                </div>
                            </div>
                            @error('phone_number')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Email Address -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.email') }}
                            </label>
                            <div class="relative">
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('email') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_email') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                            </div>
                            @error('email')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.address') }}
                            </label>
                            <div class="relative">
                                <textarea id="address"
                                          name="address"
                                          rows="3"
                                          class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('address') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                          placeholder="{{ __('churches.enter_address') }}">{{ old('address') }}</textarea>
                                <div class="absolute top-3 right-3 pointer-events-none">
                                    <i class="fas fa-map-marked-alt text-gray-400"></i>
                                </div>
                            </div>
                            @error('address')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- District -->
                        <div>
                            <label for="district" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.district') }}
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="district"
                                       name="district"
                                       value="{{ old('district') }}"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('district') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_district') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-map text-gray-400"></i>
                                </div>
                            </div>
                            @error('district')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Region -->
                        <div>
                            <label for="region" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.region') }}
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="region"
                                       name="region"
                                       value="{{ old('region') }}"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('region') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="{{ __('churches.enter_region') }}">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-globe-africa text-gray-400"></i>
                                </div>
                            </div>
                            @error('region')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Date Established -->
                        <div>
                            <label for="date_established" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.date_established') }}
                            </label>
                            <div class="relative">
                                <input type="date"
                                       id="date_established"
                                       name="date_established"
                                       value="{{ old('date_established') }}"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('date_established') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                            </div>
                            @error('date_established')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Parent Church -->
                        <div>
                            <label for="parent_church_id" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.parent_church') }}
                            </label>
                            <div class="relative">
                                <select name="parent_church_id"
                                        id="parent_church_id"
                                        class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('parent_church_id') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror">
                                    <option value="">{{ __('churches.no_parent_church') }}</option>
                                    @foreach ($parentChurches as $parent)
                                        <option value="{{ $parent->id }}" {{ old('parent_church_id') == $parent->id ? 'selected' : '' }}>
                                            {{ $parent->name }} ({{ __('churches.' . strtolower($parent->level->value)) }})
                                        </option>
                                    @endforeach
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-sitemap text-gray-400"></i>
                                </div>
                            </div>
                            @error('parent_church_id')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demographics Card -->
            <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-pie mr-3 text-purple-600"></i>
                        {{ __('churches.demographics') }}
                    </h3>
                    <p class="mt-1 text-sm text-gray-600">{{ __('churches.church_demographics_description') }}</p>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Youth Count -->
                        <div>
                            <label for="youth_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.youth_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_13_25') }})</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       id="youth_count"
                                       name="youth_count"
                                       value="{{ old('youth_count', 0) }}"
                                       min="0"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('youth_count') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-users text-gray-400"></i>
                                </div>
                            </div>
                            @error('youth_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Young Adults Count -->
                        <div>
                            <label for="young_adults_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.young_adults_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_26_40') }})</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       id="young_adults_count"
                                       name="young_adults_count"
                                       value="{{ old('young_adults_count', 0) }}"
                                       min="0"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('young_adults_count') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-graduate text-gray-400"></i>
                                </div>
                            </div>
                            @error('young_adults_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Children Count -->
                        <div>
                            <label for="children_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.children_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_0_12') }})</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       id="children_count"
                                       name="children_count"
                                       value="{{ old('children_count', 0) }}"
                                       min="0"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('children_count') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-child text-gray-400"></i>
                                </div>
                            </div>
                            @error('children_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>

                        <!-- Elders Count -->
                        <div>
                            <label for="elders_count" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('churches.elders_count') }}
                                <span class="text-xs text-gray-500">({{ __('churches.age_41_plus') }})</span>
                            </label>
                            <div class="relative">
                                <input type="number"
                                       id="elders_count"
                                       name="elders_count"
                                       value="{{ old('elders_count', 0) }}"
                                       min="0"
                                       class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 @error('elders_count') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                                       placeholder="0">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user-tie text-gray-400"></i>
                                </div>
                            </div>
                            @error('elders_count')
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                    </div>

                    <!-- Total Count Display -->
                    <div class="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-calculator text-purple-600 mr-2"></i>
                                <span class="text-sm font-medium text-purple-900">{{ __('churches.total_members') }}</span>
                            </div>
                            <span class="text-lg font-bold text-purple-900"
                                  x-text="getTotalMembers()"
                                  x-data="{
                                      getTotalMembers() {
                                          const youth = parseInt(document.getElementById('youth_count').value) || 0;
                                          const youngAdults = parseInt(document.getElementById('young_adults_count').value) || 0;
                                          const children = parseInt(document.getElementById('children_count').value) || 0;
                                          const elders = parseInt(document.getElementById('elders_count').value) || 0;
                                          return youth + youngAdults + children + elders;
                                      }
                                  }"
                                  @input.window="$nextTick(() => { $el.textContent = getTotalMembers(); })">
                                0
                            </span>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6">
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    {{ __('churches.fields_required_note') }}
                </div>

                <div class="flex items-center space-x-4">
                    <a href="{{ route('churches.index') }}"
                       class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        {{ __('common.cancel') }}
                    </a>
                    <button type="submit"
                            class="inline-flex items-center px-8 py-3 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
                        <i class="fas fa-save mr-2"></i>
                        <span x-text="getContextualButtonText()">{{ __('churches.create_church') }}</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    function churchForm() {
        return {
            selectedLevel: '{{ old('level') }}',

            updateParentChurches() {
                // This could be enhanced to filter parent churches based on selected level
                console.log('Level changed to:', this.selectedLevel);
            },

            getContextualTitle() {
                const titles = {
                    'National': '{{ __('churches.create_national_church') }}',
                    'Diocese': '{{ __('churches.create_diocese_church') }}',
                    'Local': '{{ __('churches.create_local_church') }}',
                    'Parish': '{{ __('churches.create_parish_church') }}',
                    'Branch': '{{ __('churches.create_branch_church') }}'
                };
                return titles[this.selectedLevel] || '{{ __('churches.add_new_church') }}';
            },

            getContextualButtonText() {
                const buttonTexts = {
                    'National': '{{ __('churches.create_national') }}',
                    'Diocese': '{{ __('churches.create_diocese') }}',
                    'Local': '{{ __('churches.create_local') }}',
                    'Parish': '{{ __('churches.create_parish') }}',
                    'Branch': '{{ __('churches.create_branch') }}'
                };
                return buttonTexts[this.selectedLevel] || '{{ __('churches.create_church') }}';
            }
        }
    }
</script>
@endpush
@endsection
<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Church;
use App\Models\OTP;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;
use Carbon\Carbon;

use App\Services\NotificationService;
use App\Services\OTPService;
use App\Services\ChurchHierarchyService;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    protected $notificationService;
    protected $otpService;
    protected $hierarchyService;

    public function __construct(NotificationService $notificationService, OTPService $otpService, ChurchHierarchyService $hierarchyService)
    {
        $this->notificationService = $notificationService;
        $this->otpService = $otpService;
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth')->except(['showLoginForm', 'login', 'showRegisterForm', 'register', 'showPasswordRequestForm', 'requestPasswordReset', 'showPasswordResetForm', 'resetPassword']);
        $this->middleware('permission:manage-users', ['only' => ['index', 'create', 'store', 'edit', 'update', 'destroy']]);
    }

    // List all users
    public function index(Request $request)
    {
        $currentUser = Auth::user();

        // Get churches user can access based on hierarchy
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        $accessibleChurchIds = $accessibleChurches->pluck('id')->toArray();

        $query = User::with(['church', 'roles'])
                    ->whereIn('church_id', $accessibleChurchIds);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        // Church filter (only from accessible churches)
        if ($request->filled('church')) {
            $churchId = $request->church;
            if (in_array($churchId, $accessibleChurchIds)) {
                $query->where('church_id', $churchId);
            }
        }

        // Role filter
        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Status filter
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'first_login':
                    $query->where('is_first_login', true);
                    break;
            }
        }

        $users = $query->paginate(10);

        // Get data for dropdowns (only accessible churches)
        $churches = $accessibleChurches->sortBy('name');
        $roles = Role::orderBy('name')->get();

        return view('users.index', compact('users', 'churches', 'roles'));
    }

    // Show create user form
    public function create()
    {
        $currentUser = Auth::user();

        // Get churches user can manage (where they can create users)
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        $churches = $accessibleChurches->sortBy('name');
        $roles = Role::all();

        return view('users.create', compact('churches', 'roles'));
    }

    // Store new user
    public function store(Request $request)
    {
        $currentUser = Auth::user();

        $request->validate([
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|unique:users,phone_number',
            'church_id' => 'required|exists:churches,id',
            'role' => 'required|string|exists:roles,name',
        ]);

        // Check if user can create users in the specified church
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        if (!$accessibleChurches->contains('id', $request->church_id)) {
            abort(403, 'You do not have permission to create users in this church.');
        }

        $password = 'FPCT' . Str::random(6) . rand(10, 99); // More secure temporary password
        $user = User::create([
            'full_name' => $request->email, // Temporary, user will update this
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'password' => Hash::make($password),
            'church_id' => $request->church_id,
            'role' => $request->role,
            'is_first_login' => true,
            'is_active' => true,
        ]);

        $user->assignRole($request->role);

        // Generate OTP and send welcome notification with both password and OTP
        $otp = $this->otpService->generateRegistrationOTP($user);
        $this->sendWelcomeNotification($user, $password, $otp);

        return redirect()->route('users.index')->with('success', 'User created successfully! Login credentials have been sent to: ' . $request->email);
    }

        public function register(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|unique:users,phone_number',
            'church_id' => 'required|exists:churches,id',
            'role' => 'required|string|exists:roles,name',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::create([
            'full_name' => $request->full_name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'password' => Hash::make($request->password),
            'church_id' => $request->church_id,
            'role' => $request->role,
            'is_first_login' => true,
            'is_active' => false, // Pending admin approval
        ]);

        $user->assignRole($request->role);

        // Generate OTP and send welcome notification with password and OTP
        $otp = $this->otpService->generateRegistrationOTP($user);
        $this->sendWelcomeNotification($user, $request->password, $otp);

        return redirect()->route('login')->with('success', 'Registration successful. Please check your email/phone for login credentials and await admin approval.');
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($request->only('email', 'password'))) {
            $request->session()->regenerate();
            $user = Auth::user();

            // Check if user is active
            if (!$user->is_active) {
                Auth::logout();
                return back()->withErrors(['email' => 'Your account has been deactivated. Please contact an administrator.']);
            }

            if ($user->is_first_login) {
                return redirect()->route('user.first-login');
            }

            // Set user's preferred language
            if ($user->locale) {
                session(['locale' => $user->locale]);
                app()->setLocale($user->locale);
            }

            // Check if there's an intended URL (where user was trying to go before login)
            if ($intendedUrl = session()->pull('url.intended')) {
                // Make sure we don't redirect to login page itself
                if ($intendedUrl !== route('login') && $intendedUrl !== url('/login')) {
                    return redirect($intendedUrl);
                }
            }

            // Default redirect based on user role and preferences
            if ($user->hasRole('National Treasurer') || $user->hasRole('Diocese Treasurer') ||
                $user->hasRole('Local Treasurer') || $user->hasRole('Parish Treasurer') ||
                $user->hasRole('Branch Treasurer')) {
                return redirect()->route('financial.dashboard');
            }

            return redirect()->route('dashboard.index');
        }

        throw ValidationException::withMessages([
            'email' => ['The provided credentials do not match our records.'],
        ]);
    }

    public function showRegisterForm()
    {
        $churches = Church::all();
        $roles = Role::pluck('name')->toArray();
        return view('auth.register', compact('churches', 'roles'));
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function logout(Request $request)
    {
        // Clear all session data
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        // Clear any remember me tokens
        if ($request->user()) {
            $request->user()->tokens()->delete();
        }

        // Add cache control headers to prevent back button access
        $response = redirect()->route('login')->with('success', 'You have been logged out successfully.');

        $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', 'Thu, 01 Jan 1970 00:00:00 GMT');

        return $response;
    }

    // Show user details
    public function show(User $user)
    {
        $currentUser = Auth::user();

        // Check if current user can view this user
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        if (!$accessibleChurches->contains('id', $user->church_id)) {
            abort(403, 'You do not have permission to view this user.');
        }

        $user->load('church', 'roles');
        return view('users.show', compact('user'));
    }

    // Show edit user form
    public function edit(User $user)
    {
        $currentUser = Auth::user();

        // Check if current user can edit this user
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        if (!$accessibleChurches->contains('id', $user->church_id)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        // Only show accessible churches for church assignment
        $churches = $accessibleChurches->sortBy('name');
        $roles = Role::all();
        return view('users.edit', compact('user', 'churches', 'roles'));
    }

    // Update user
    public function update(Request $request, User $user)
    {
        $currentUser = Auth::user();

        // Check if current user can edit this user
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($currentUser);
        if (!$accessibleChurches->contains('id', $user->church_id)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone_number' => 'required|string|unique:users,phone_number,' . $user->id,
            'church_id' => 'required|exists:churches,id',
            'role' => 'required|string|exists:roles,name',
            'is_active' => 'boolean',
        ]);

        // Check if the new church is accessible to current user
        if (!$accessibleChurches->contains('id', $request->church_id)) {
            abort(403, 'You do not have permission to assign users to this church.');
        }

        $user->update([
            'full_name' => $request->full_name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'church_id' => $request->church_id,
            'role' => $request->role,
            'is_active' => $request->boolean('is_active', $user->is_active),
        ]);

        $user->syncRoles($request->role);

        return redirect()->route('users.index')->with('success', 'User updated successfully.');
    }

    // Delete user (soft delete)
    public function destroy(User $user)
    {
        $user->delete();
        return redirect()->route('users.index')->with('success', 'User moved to trash.');
    }

    // Show profile page
    public function profile()
    {
        $user = Auth::user();
        return view('users.profile', compact('user'));
    }

    // Update profile
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone_number' => 'required|string|unique:users,phone_number,' . $user->id,
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'bio' => 'nullable|string|max:1000',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        $updateData = [
            'full_name' => $request->full_name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'date_of_birth' => $request->date_of_birth,
            'gender' => $request->gender,
            'address' => $request->address,
            'bio' => $request->bio,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
        ];

        // Handle profile picture upload
        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture if exists
            if ($user->profile_picture && file_exists(storage_path('app/public/' . $user->profile_picture))) {
                unlink(storage_path('app/public/' . $user->profile_picture));
            }

            // Store new profile picture
            $path = $request->file('profile_picture')->store('profile-pictures', 'public');
            $updateData['profile_picture'] = $path;
        }

        $user->update($updateData);

        return redirect()->route('user.profile')
            ->with('success', 'Profile updated successfully.')
            ->with('toast', [
                'type' => 'success',
                'message' => 'Profile updated successfully.'
            ]);
    }

    // Update password
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
        ], [
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
        ]);

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('user.profile')
            ->with('success', 'Password changed successfully.')
            ->with('toast', [
                'type' => 'success',
                'message' => 'Password changed successfully.'
            ]);
    }

    // Verify current password via AJAX
    public function verifyCurrentPassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
        ]);

        $user = Auth::user();
        $isValid = Hash::check($request->current_password, $user->password);

        return response()->json([
            'valid' => $isValid,
            'message' => $isValid ? 'Password is correct' : 'Password is incorrect'
        ]);
    }

    // Show first login OTP verification form
    public function showFirstLogin()
    {
        if (!Auth::user()->is_first_login) {
            return redirect()->route('dashboard.index');
        }
        return view('users.first-login');
    }

    // Verify OTP for first login
    public function verifyFirstLogin(Request $request)
    {
        $request->validate([
            'otp' => 'required|string|size:6',
        ]);

        $user = Auth::user();
        $otp = OTP::where('user_id', $user->id)
            ->where('otp', $request->otp)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if (!$otp) {
            return back()->withErrors(['otp' => 'Invalid or expired OTP.']);
        }

        $user->update(['is_first_login' => false]);
        $otp->delete();

        return redirect()->route('user.profile')->with('success', 'First login verified. Please complete your profile information.');
    }

    // Show password request form
    public function showPasswordRequestForm()
    {
        return view('auth.request');
    }

    // Show password reset form
    public function showPasswordResetForm(Request $request)
    {
        return view('auth.reset', [
            'email' => $request->get('email', ''),
            'token' => $request->get('token', '')
        ]);
    }

    // Request password reset OTP
    public function requestPasswordReset(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
        ]);

        $user = User::where('email', $request->email)->first();
        $temporaryPassword = 'FPCT' . Str::random(6) . rand(10, 99);
        $otp = $this->otpService->generatePasswordResetOTP($user);

        // Update user with temporary password
        $user->update(['password' => Hash::make($temporaryPassword)]);

        // Send password reset notification with new password and OTP
        $this->notificationService->sendPasswordResetNotification($user, $temporaryPassword, $otp);

        return redirect()->route('password.reset', ['email' => $request->email])->with('success', 'New password and OTP sent to your email/phone.');
    }

    // Verify password reset OTP and reset password
    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
            'otp' => 'required|string|size:6',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::where('email', $request->email)->first();

        // Validate OTP using OTPService
        if (!$this->otpService->validatePasswordResetOTP($user, $request->otp)) {
            return back()->withErrors(['otp' => 'Invalid or expired OTP.']);
        }

        $user->update(['password' => Hash::make($request->password)]);

        return redirect()->route('login')->with('success', 'Password reset successfully.');
    }





    // Send welcome notification with password and OTP
    private function sendWelcomeNotification(User $user, string $password, string $otp)
    {
        try {
            $this->notificationService->sendWelcomeEmail($user, $password, $otp);
            $this->notificationService->sendWelcomeSMS($user, $password, $otp);
        } catch (\Exception $e) {
            Log::error('Failed to send welcome notification: ' . $e->getMessage());
        }
    }
}

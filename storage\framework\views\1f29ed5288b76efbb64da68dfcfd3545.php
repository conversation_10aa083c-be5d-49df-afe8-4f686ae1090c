<?php $__env->startSection('title', __('churches.church_reports')); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                <?php echo e(__('churches.church_reports')); ?>

            </h2>
            <p class="mt-1 text-sm text-gray-500">
                <?php echo e(__('churches.generate_reports_description')); ?>

            </p>
        </div>
        <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
            <a href="<?php echo e(route('churches.index')); ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-arrow-left mr-2"></i>
                <?php echo e(__('churches.back_to_churches')); ?>

            </a>
        </div>
    </div>

    <!-- Available Levels -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?php echo e(__('churches.available_levels')); ?>

            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $availableLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $levelData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 border border-blue-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-church text-2xl text-blue-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-lg font-semibold text-gray-900">
                                    <?php echo e(__('churches.' . strtolower($levelData['name']))); ?>

                                </h4>
                                <p class="text-sm text-gray-600">
                                    <?php echo e($levelData['count']); ?> <?php echo e(__('churches.churches_available')); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <button onclick="openReportModal('<?php echo e($levelData['level']->value); ?>')"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-file-alt mr-2"></i>
                        <?php echo e(__('churches.generate_report')); ?>

                    </button>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Quick Statistics -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <?php echo e(__('churches.quick_statistics')); ?>

            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6" id="quick-stats">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="total-churches">-</div>
                    <div class="text-sm text-gray-600"><?php echo e(__('churches.total_churches')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="total-users">-</div>
                    <div class="text-sm text-gray-600"><?php echo e(__('churches.total_users')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" id="total-demographics">-</div>
                    <div class="text-sm text-gray-600"><?php echo e(__('churches.total_demographics')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600" id="total-levels"><?php echo e(count($availableLevels)); ?></div>
                    <div class="text-sm text-gray-600"><?php echo e(__('churches.active_levels')); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Generation Modal -->
<div id="reportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">
                    <?php echo e(__('churches.generate_report')); ?>

                </h3>
                <button onclick="closeReportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="reportForm" method="POST" action="<?php echo e(route('church-reports.generate')); ?>">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="level" id="selectedLevel">
                
                <div class="space-y-4">
                    <!-- Format Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo e(__('churches.report_format')); ?>

                        </label>
                        <select name="format" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="view"><?php echo e(__('churches.view_online')); ?></option>
                            <option value="pdf"><?php echo e(__('churches.download_pdf')); ?></option>
                            <option value="excel"><?php echo e(__('churches.download_excel')); ?></option>
                        </select>
                    </div>
                    
                    <!-- Report Options -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo e(__('churches.report_options')); ?>

                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="include_demographics" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_demographics')); ?></span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_leadership" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_leadership')); ?></span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_statistics" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_statistics')); ?></span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_contact_info" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_contact_info')); ?></span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_user_details" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_user_details')); ?></span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_establishment_info" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700"><?php echo e(__('churches.include_establishment_info')); ?></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeReportModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        <?php echo e(__('common.cancel')); ?>

                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-download mr-2"></i>
                        <?php echo e(__('churches.generate')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Load quick statistics
    document.addEventListener('DOMContentLoaded', function() {
        loadQuickStatistics();
    });

    function loadQuickStatistics() {
        fetch('<?php echo e(route("church-reports.statistics")); ?>')
            .then(response => response.json())
            .then(data => {
                document.getElementById('total-churches').textContent = data.total_churches;
                document.getElementById('total-users').textContent = data.total_users;
                
                const totalDemographics = data.demographics.youth + data.demographics.young_adults + 
                                        data.demographics.children + data.demographics.elders;
                document.getElementById('total-demographics').textContent = totalDemographics;
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
            });
    }

    function openReportModal(level) {
        document.getElementById('selectedLevel').value = level;
        document.getElementById('modalTitle').textContent = `Generate ${level} Churches Report`;
        document.getElementById('reportModal').classList.remove('hidden');
    }

    function closeReportModal() {
        document.getElementById('reportModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('reportModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeReportModal();
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/churches/reports/index.blade.php ENDPATH**/ ?>
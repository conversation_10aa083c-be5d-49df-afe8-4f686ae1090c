@extends('layouts.app')

@section('title', 'Message Details')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Message Details</h1>
                    <p class="mt-2 text-sm text-gray-600">View message content and details</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('messages.index') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Messages
                    </a>
                </div>
            </div>
        </div>

        <!-- Message Card -->
        <div class="bg-white shadow-sm rounded-xl border border-gray-200 overflow-hidden">
            <!-- Message Header -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <span class="text-lg font-medium text-blue-600">
                                {{ substr($message->sender->full_name, 0, 1) }}
                            </span>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $message->sender->full_name }}</h3>
                            <p class="text-sm text-gray-600">{{ $message->sender->email }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        @if($message->is_announcement)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <i class="fas fa-bullhorn mr-1"></i>
                                Announcement
                            </span>
                        @elseif($message->is_group_message)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-users mr-1"></i>
                                Group Message
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-user mr-1"></i>
                                Direct Message
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Message Content -->
            <div class="p-6">
                <div class="prose max-w-none">
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $message->content }}</p>
                    </div>
                </div>
            </div>

            <!-- Message Details -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Church</label>
                            <p class="text-sm text-gray-900">{{ $message->church->name ?? 'N/A' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Recipients</label>
                            <div class="text-sm text-gray-900">
                                @if($message->recipients->count() > 0)
                                    @if($message->recipients->count() <= 5)
                                        @foreach ($message->recipients as $recipient)
                                            <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 mr-1 mb-1">
                                                {{ $recipient->full_name }}
                                            </span>
                                        @endforeach
                                    @else
                                        <span class="text-gray-600">{{ $message->recipients->count() }} recipients</span>
                                    @endif
                                @else
                                    <span class="text-gray-500">No recipients</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Sent At</label>
                            <p class="text-sm text-gray-900">{{ $message->created_at ? $message->created_at->format('M d, Y \a\t g:i A') : 'Unknown' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Read Status</label>
                            @php
                                $userRecipient = $message->recipients->where('id', auth()->id())->first();
                                $readAt = $userRecipient ? $userRecipient->pivot->read_at : null;

                                // Handle different types of read_at values
                                if ($readAt) {
                                    if (is_string($readAt)) {
                                        try {
                                            $readAt = \Carbon\Carbon::parse($readAt);
                                        } catch (\Exception $e) {
                                            $readAt = null;
                                        }
                                    }
                                }
                            @endphp
                            <p class="text-sm text-gray-900">
                                @if($readAt && method_exists($readAt, 'format'))
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Read on {{ $readAt->format('M d, Y \a\t g:i A') }}
                                    </span>
                                @elseif($readAt)
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Read: {{ $readAt }}
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        Unread
                                    </span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="px-6 py-4 border-t border-gray-200 bg-white">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Message ID: {{ $message->id }}
                    </div>
                    <div class="flex items-center space-x-3">
                        @can('send-messages')
                        <a href="{{ route('messages.create') }}?reply_to={{ $message->id }}"
                           class="inline-flex items-center px-4 py-2 border border-green-300 rounded-lg text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-reply mr-2"></i>
                            Reply
                        </a>
                        @endcan
                        <a href="{{ route('messages.index') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Messages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
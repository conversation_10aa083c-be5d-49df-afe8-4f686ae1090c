# FPCT System Technical Documentation

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Revenue System Architecture](#revenue-system-architecture)
3. [SMS Integration](#sms-integration)
4. [API Documentation](#api-documentation)
5. [Database Schema](#database-schema)
6. [Event System](#event-system)
7. [Security Implementation](#security-implementation)
8. [Development Guidelines](#development-guidelines)

## Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Blade/JS)    │◄──►│   (Laravel)     │◄──►│   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
│                      │                      │
├─ Tailwind CSS       ├─ Controllers          ├─ Huduma SMS
├─ Alpine.js          ├─ Services             ├─ AzamPay
├─ Laravel Echo       ├─ Models               ├─ PostgreSQL
└─ Vite Build         ├─ Events/Observers     └─ Redis (optional)
                      └─ Queue System
```

### Key Technologies

- **Framework**: Laravel 12 with PHP 8.2+
- **Database**: PostgreSQL 13+ with Eloquent ORM
- **Frontend**: Blade templates with Tailwind CSS 4.0
- **Real-time**: Laravel Echo with WebSockets
- **Queue System**: Laravel Queues for background processing
- **SMS Service**: Huduma SMS API integration
- **Payment Gateway**: AzamPay API integration

## Revenue System Architecture

### Core Components

#### 1. Models
- `Transaction`: Core transaction model with status management
- `FinancialBalance`: Church-level balance tracking
- `Contribution`: Special contribution campaigns
- `Receipt`: Transaction receipt generation

#### 2. Services
- `RevenueService`: Main business logic for revenue operations
- `AzamPayService`: Payment gateway integration
- `ReceiptService`: Receipt generation and management
- `NotificationService`: Transaction notifications

#### 3. Controllers
- `TransactionController`: Web interface for transactions
- `RevenueController`: Revenue management dashboard
- `AzamPayController`: Payment gateway callbacks

### Transaction Flow

```
┌─────────────────┐
│ User Initiates  │
│ Transaction     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Validation &    │
│ Business Logic  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Payment         │
│ Processing      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ Status Update   │
│ & Notifications │
└─────────────────┘
```

### Transaction States

```php
enum TransactionStatus: string
{
    case PENDING = 'pending';      // Initial state
    case PROCESSING = 'processing'; // Payment in progress
    case COMPLETED = 'completed';   // Successfully completed
    case FAILED = 'failed';        // Payment failed
    case CANCELLED = 'cancelled';   // User cancelled
}
```

### Payment Methods

```php
enum PaymentMethod: string
{
    case MOBILE_MONEY = 'mobile_money';  // M-Pesa, Tigo Pesa, etc.
    case BANK_TRANSFER = 'bank_transfer'; // Direct bank transfers
    case CASH = 'cash';                  // Manual cash transactions
}
```

## SMS Integration

### Huduma SMS Service

#### Configuration
```php
// config/services.php
'hudumasms' => [
    'api_token' => env('HUDUMASMS_API_TOKEN'),
    'sender_id' => env('HUDUMASMS_SENDER_ID', '************'),
    'base_url' => env('HUDUMASMS_BASE_URL', 'https://sms-api.huduma.cloud/api/v3'),
    'callback_url' => env('HUDUMASMS_CALLBACK_URL'),
],
```

#### Service Implementation
```php
class HudumaSMSService
{
    public function sendSMS(string $phoneNumber, string $message, string $type = 'general', ?int $userId = null): bool
    public function sendBulkSMS(array $phoneNumbers, string $message): array
    public function sendOTP(string $phoneNumber, string $otp, string $appName = 'FPCT', ?int $userId = null): bool
    public function sendTransactionSMS(...): bool
    public function getBalance(): array
    public function testConnection(): bool
}
```

#### SMS Types
- `general`: General SMS messages
- `otp`: OTP verification codes
- `welcome`: Welcome messages for new users
- `announcement`: Church announcements
- `transaction`: Transaction confirmations
- `password_reset`: Password reset notifications

### Transaction SMS Format

```
FPCT Transaction Completed!
Type: Revenue Collection
Amount: TZS 50,000.00
From: St. John Parish
To: Dar es Salaam Local Church
Purpose: Monthly revenue collection
Ref: REF-ABC123-20240101
ID: TXN-XYZ789-20240101120000
```

## API Documentation

### Authentication

All API endpoints require Bearer token authentication:
```http
Authorization: Bearer {token}
```

### Revenue API Endpoints

#### Create Transaction
```http
POST /api/transactions
Content-Type: application/json

{
  "amount": 50000.00,
  "payment_method": "mobile_money",
  "description": "Monthly revenue collection",
  "payment_details": {
    "phone_number": "+************",
    "provider": "mpesa"
  }
}
```

#### Get Transactions
```http
GET /api/transactions?page=1&per_page=20&status=completed
```

#### Get Financial Statistics
```http
GET /api/churches/{id}/revenue-statistics?period=month
```

### SMS API Endpoints

#### Send SMS
```http
POST /api/sms/send
Content-Type: application/json

{
  "phone_number": "+************",
  "message": "Your message content",
  "type": "general"
}
```

#### SMS Callback
```http
POST /api/sms/callback
Content-Type: application/json

{
  "smscId": "12345",
  "status": "DELIVERED",
  "recipient": "************",
  "thirdPartyRef": "fpct-123-abc"
}
```

### AzamPay Integration

#### Payment Callback
```http
POST /api/azampay/callback
Content-Type: application/json

{
  "externalId": "TXN-ABC123-20240101120000",
  "transactionId": "azampay_txn_123",
  "status": "success",
  "amount": 50000.00,
  "currency": "TZS"
}
```

## Database Schema

### Core Tables

#### transactions
```sql
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    reference_number VARCHAR(255) UNIQUE NOT NULL,
    from_church_id BIGINT REFERENCES churches(id),
    to_church_id BIGINT REFERENCES churches(id),
    initiated_by_user_id BIGINT REFERENCES users(id),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'TZS',
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_details JSONB,
    provider_response JSONB,
    created_at TIMESTAMP,
    completed_at TIMESTAMP
);
```

#### financial_balances
```sql
CREATE TABLE financial_balances (
    id BIGSERIAL PRIMARY KEY,
    church_id BIGINT REFERENCES churches(id) UNIQUE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT NOW()
);
```

#### sms_logs
```sql
CREATE TABLE sms_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    phone_number VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'general',
    status VARCHAR(20) DEFAULT 'pending',
    provider VARCHAR(20) DEFAULT 'huduma',
    provider_message_id VARCHAR(255),
    third_party_ref VARCHAR(255),
    credits_used INTEGER DEFAULT 1,
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    failed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes

```sql
-- Transaction indexes
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_from_church ON transactions(from_church_id);
CREATE INDEX idx_transactions_to_church ON transactions(to_church_id);
CREATE INDEX idx_transactions_completed_at ON transactions(completed_at);

-- SMS logs indexes
CREATE INDEX idx_sms_logs_phone ON sms_logs(phone_number);
CREATE INDEX idx_sms_logs_status ON sms_logs(status);
CREATE INDEX idx_sms_logs_type ON sms_logs(type);
```

## Event System

### Transaction Events

#### TransactionCompleted Event
```php
class TransactionCompleted implements ShouldBroadcast
{
    public Transaction $transaction;
    
    public function broadcastOn()
    {
        return [
            new Channel('user.' . $this->transaction->initiated_by_user_id),
            new Channel('church.' . $this->transaction->from_church_id),
            new Channel('church.' . $this->transaction->to_church_id),
        ];
    }
}
```

#### TransactionObserver
```php
class TransactionObserver
{
    public function updated(Transaction $transaction): void
    {
        if ($transaction->isDirty('status') && 
            $transaction->status === TransactionStatus::COMPLETED) {
            
            event(new TransactionCompleted($transaction));
            $this->sendTransactionCompletedSMS($transaction);
        }
    }
}
```

### Event Registration
```php
// app/Providers/AppServiceProvider.php
public function boot(): void
{
    Transaction::observe(TransactionObserver::class);
}
```

## Security Implementation

### Authentication & Authorization

#### Role-Based Access Control
```php
// Middleware for transaction access
public function handle($request, Closure $next)
{
    $user = $request->user();
    
    if (!$user->hasPermissionTo('manage_transactions')) {
        abort(403, 'Unauthorized');
    }
    
    return $next($request);
}
```

#### Church-Level Permissions
```php
// Users can only manage transactions for their church level or below
public function canManageChurch(Church $church): bool
{
    return $this->church->canManage($church);
}
```

### Data Validation

#### Transaction Validation
```php
$request->validate([
    'amount' => 'required|numeric|min:1|max:********',
    'payment_method' => 'required|in:mobile_money,bank_transfer,cash',
    'description' => 'required|string|max:500',
    'payment_details' => 'required|array',
    'payment_details.phone_number' => 'required_if:payment_method,mobile_money|regex:/^\+255[67]\d{8}$/',
]);
```

### API Security

#### Rate Limiting
```php
// routes/api.php
Route::middleware(['auth:sanctum', 'throttle:60,1'])->group(function () {
    Route::apiResource('transactions', TransactionController::class);
});
```

#### CSRF Protection
```php
// All web routes protected by CSRF middleware
Route::middleware(['web', 'auth', 'verified'])->group(function () {
    Route::resource('transactions', TransactionController::class);
});
```

## Development Guidelines

### Code Standards

#### PSR-12 Compliance
- Use PSR-12 coding standards
- Run `./vendor/bin/pint` for code formatting
- Use type declarations for all methods

#### Testing Requirements
```php
// Feature test example
public function test_user_can_create_transaction()
{
    $user = User::factory()->create();
    $church = Church::factory()->create();
    
    $response = $this->actingAs($user)
        ->post('/api/transactions', [
            'amount' => 50000,
            'payment_method' => 'mobile_money',
            'description' => 'Test transaction',
            'payment_details' => [
                'phone_number' => '+************',
                'provider' => 'mpesa'
            ]
        ]);
    
    $response->assertStatus(201);
    $this->assertDatabaseHas('transactions', [
        'amount' => 50000,
        'initiated_by_user_id' => $user->id
    ]);
}
```

### Deployment Considerations

#### Environment Configuration
- Use environment variables for all external service credentials
- Implement proper error handling and logging
- Set up monitoring for transaction failures

#### Performance Optimization
- Use database indexes for frequently queried fields
- Implement caching for financial statistics
- Use queues for SMS sending and heavy operations

#### Monitoring
- Log all transaction state changes
- Monitor SMS delivery rates
- Track API response times
- Set up alerts for failed transactions

---

**For technical support and development questions, refer to the development team or system architecture documentation.**

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Enums\PaymentMethod;
use Illuminate\Support\Str;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'reference_number',
        'from_church_id',
        'to_church_id',
        'initiated_by_user_id',
        'approved_by_user_id',
        'amount',
        'currency',
        'type',
        'status',
        'description',
        'notes',
        'contribution_id',
        'payment_method',
        'payment_provider',
        'payment_details',
        'provider_transaction_id',
        'provider_response',
        'initiated_at',
        'approved_at',
        'completed_at',
        'failed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'type' => TransactionType::class,
        'status' => TransactionStatus::class,
        'payment_method' => PaymentMethod::class,
        'payment_details' => 'array',
        'provider_response' => 'array',
        'initiated_at' => 'datetime',
        'approved_at' => 'datetime',
        'completed_at' => 'datetime',
        'failed_at' => 'datetime',
    ];

    // Relationships
    public function fromChurch(): BelongsTo
    {
        return $this->belongsTo(Church::class, 'from_church_id');
    }

    public function toChurch(): BelongsTo
    {
        return $this->belongsTo(Church::class, 'to_church_id');
    }

    public function initiatedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiated_by_user_id');
    }

    public function approvedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by_user_id');
    }

    public function contribution(): BelongsTo
    {
        return $this->belongsTo(Contribution::class);
    }

    public function receipt(): HasOne
    {
        return $this->hasOne(Receipt::class);
    }

    // Scopes
    public function scopeByStatus($query, TransactionStatus $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, TransactionType $type)
    {
        return $query->where('type', $type);
    }

    public function scopeForChurch($query, int $churchId)
    {
        return $query->where(function ($q) use ($churchId) {
            $q->where('from_church_id', $churchId)
              ->orWhere('to_church_id', $churchId);
        });
    }

    /**
     * Scope to get transactions for a church and its hierarchy
     * Treasurers should only see transactions involving their church level and below
     */
    public function scopeForChurchHierarchy($query, Church $church)
    {
        // Get all descendant churches (churches below this level)
        $descendantIds = $church->getAllDescendants()->pluck('id')->toArray();
        $churchIds = array_merge([$church->id], $descendantIds);

        return $query->where(function ($q) use ($churchIds) {
            $q->whereIn('from_church_id', $churchIds)
              ->orWhereIn('to_church_id', $churchIds);
        });
    }

    /**
     * Scope to get transactions relevant to a treasurer's role
     * This includes:
     * - Transactions from their church to higher levels
     * - Transactions from lower levels to their church
     * - Internal transactions within their hierarchy
     */
    public function scopeForTreasurerRole($query, Church $church)
    {
        // Get all descendant churches (churches below this level)
        $descendantIds = $church->getAllDescendants()->pluck('id')->toArray();
        $allRelevantIds = array_merge([$church->id], $descendantIds);

        return $query->where(function ($q) use ($church, $allRelevantIds) {
            $q->where(function ($subQ) use ($allRelevantIds) {
                // Transactions within their hierarchy (from any church in hierarchy to any church in hierarchy)
                $subQ->whereIn('from_church_id', $allRelevantIds)
                     ->whereIn('to_church_id', $allRelevantIds);
            })
            ->orWhere(function ($subQ) use ($church, $allRelevantIds) {
                // Transactions from their hierarchy to higher levels
                $subQ->whereIn('from_church_id', $allRelevantIds)
                     ->where('to_church_id', $church->parent_church_id ?? 0);
            })
            ->orWhere(function ($subQ) use ($church, $allRelevantIds) {
                // Transactions from higher levels to their hierarchy
                $subQ->where('from_church_id', $church->parent_church_id ?? 0)
                     ->whereIn('to_church_id', $allRelevantIds);
            });
        });
    }

    public function scopeIncoming($query, int $churchId)
    {
        return $query->where('to_church_id', $churchId);
    }

    public function scopeOutgoing($query, int $churchId)
    {
        return $query->where('from_church_id', $churchId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', TransactionStatus::getActiveStatuses());
    }

    // Business Logic Methods
    public static function generateTransactionId(): string
    {
        return 'TXN-' . strtoupper(Str::random(8)) . '-' . now()->format('YmdHis');
    }

    public static function generateReferenceNumber(): string
    {
        return 'REF-' . strtoupper(Str::random(6)) . '-' . now()->format('Ymd');
    }

    public function canBeApproved(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, [TransactionStatus::PENDING, TransactionStatus::PROCESSING]);
    }

    public function approve(User $approver): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => TransactionStatus::PROCESSING,
            'approved_by_user_id' => $approver->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    public function complete(): bool
    {
        if ($this->status !== TransactionStatus::PROCESSING) {
            return false;
        }

        $this->update([
            'status' => TransactionStatus::COMPLETED,
            'completed_at' => now(),
        ]);

        // Update church balances
        $this->updateChurchBalances();

        // Update contribution if applicable
        if ($this->contribution_id) {
            $this->contribution->addContribution($this->amount);
        }

        return true;
    }

    public function fail(string $reason = null): bool
    {
        if (!in_array($this->status, [TransactionStatus::PENDING, TransactionStatus::PROCESSING])) {
            return false;
        }

        $this->update([
            'status' => TransactionStatus::FAILED,
            'failed_at' => now(),
            'notes' => $reason ? $this->notes . "\nFailure reason: " . $reason : $this->notes,
        ]);

        return true;
    }

    protected function updateChurchBalances(): void
    {
        // Deduct from sender
        $fromBalance = FinancialBalance::firstOrCreate(['church_id' => $this->from_church_id]);
        if (!$fromBalance->deduct((float)$this->amount)) {
            throw new \Exception('Insufficient balance for transaction completion.');
        }

        // Add to receiver
        $toBalance = FinancialBalance::firstOrCreate(['church_id' => $this->to_church_id]);
        $toBalance->add((float)$this->amount);
    }
}

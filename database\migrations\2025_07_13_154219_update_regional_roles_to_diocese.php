<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update role names from Regional to Diocese
        $roleUpdates = [
            'Regional Secretary' => 'Diocese Secretary',
            'Regional Treasurer' => 'Diocese Treasurer',
            'Regional IT' => 'Diocese IT'
        ];

        foreach ($roleUpdates as $oldName => $newName) {
            // Check if old role exists and new role doesn't exist
            $oldRole = DB::table('roles')->where('name', $oldName)->first();
            $newRole = DB::table('roles')->where('name', $newName)->first();

            if ($oldRole && !$newRole) {
                // Update the role name
                DB::table('roles')
                    ->where('name', $oldName)
                    ->update(['name' => $newName]);
            } elseif ($oldRole && $newRole) {
                // Both exist, migrate users from old to new role and delete old
                DB::table('model_has_roles')
                    ->where('role_id', $oldRole->id)
                    ->update(['role_id' => $newRole->id]);

                // Delete the old role
                DB::table('roles')->where('id', $oldRole->id)->delete();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert role names from Diocese back to Regional
        $roleUpdates = [
            'Diocese Secretary' => 'Regional Secretary',
            'Diocese Treasurer' => 'Regional Treasurer',
            'Diocese IT' => 'Regional IT'
        ];

        foreach ($roleUpdates as $oldName => $newName) {
            DB::table('roles')
                ->where('name', $oldName)
                ->update(['name' => $newName]);
        }

        // Update user roles in model_has_roles table
        foreach ($roleUpdates as $oldName => $newName) {
            $role = DB::table('roles')->where('name', $newName)->first();
            if ($role) {
                DB::table('model_has_roles')
                    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->where('roles.name', $oldName)
                    ->update(['model_has_roles.role_id' => $role->id]);
            }
        }
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\ContributionStatus;
use App\Enums\ChurchLevel;
use Carbon\Carbon;

class Contribution extends Model
{
    protected $fillable = [
        'name',
        'description',
        'created_by_church_id',
        'created_by_user_id',
        'target_amount',
        'collected_amount',
        'start_date',
        'end_date',
        'status',
        'type',
        'collection_scope',
        'target_churches',
        'is_mandatory',
        'instructions',
    ];

    protected $casts = [
        'target_amount' => 'decimal:2',
        'collected_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'status' => ContributionStatus::class,
        'target_churches' => 'array',
        'is_mandatory' => 'boolean',
    ];

    // Relationships
    public function createdByChurch(): BelongsTo
    {
        return $this->belongsTo(Church::class, 'created_by_church_id');
    }

    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', ContributionStatus::ACTIVE);
    }

    public function scopeForChurchLevel($query, ChurchLevel $level)
    {
        return $query->where('collection_scope', $level->value);
    }

    public function scopeCurrentlyActive($query)
    {
        return $query->where('status', ContributionStatus::ACTIVE)
                    ->where('start_date', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    // Business Logic Methods
    public function getProgressPercentage(): float
    {
        if (!$this->target_amount || $this->target_amount <= 0) {
            return 0;
        }
        
        return min(100, ($this->collected_amount / $this->target_amount) * 100);
    }

    public function getRemainingAmount(): float
    {
        if (!$this->target_amount) {
            return 0;
        }
        
        return max(0, $this->target_amount - $this->collected_amount);
    }

    public function isExpired(): bool
    {
        return $this->end_date && $this->end_date->isPast();
    }

    public function canReceiveContributions(): bool
    {
        return $this->status->canReceiveContributions() && !$this->isExpired();
    }

    public function addContribution(float $amount): void
    {
        $this->increment('collected_amount', $amount);
        
        // Auto-complete if target reached
        if ($this->target_amount && $this->collected_amount >= $this->target_amount) {
            $this->update(['status' => ContributionStatus::COMPLETED]);
        }
    }

    public function getTargetChurches(): array
    {
        if ($this->target_churches) {
            return $this->target_churches;
        }

        // If no specific churches, return all churches at the collection scope level
        return Church::where('level', $this->collection_scope)->pluck('id')->toArray();
    }
}

<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use App\Models\SmsLog;

class HudumaSMSService
{
    protected Client $httpClient;
    protected string $apiToken;
    protected string $senderId;
    protected string $baseUrl;
    protected ?string $callbackUrl;

    public function __construct()
    {
        $this->apiToken = config('services.hudumasms.api_token');
        $this->senderId = config('services.hudumasms.sender_id');
        $this->baseUrl = config('services.hudumasms.base_url', 'https://sms-api.huduma.cloud');
        $this->callbackUrl = config('services.hudumasms.callback_url');

        $clientConfig = [
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
            'headers' => [
                'X-Huduma' => 'Bearer ' . $this->apiToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        ];

        // In local development, disable SSL verification to avoid certificate issues
        if (config('app.env') === 'local') {
            $clientConfig['verify'] = false;
        }

        $this->httpClient = new Client($clientConfig);
    }

    /**
     * Send SMS to a single recipient
     */
    public function sendSMS(string $phoneNumber, string $message, string $type = 'general', ?int $userId = null): bool
    {
        // Create SMS log entry
        $smsLog = SmsLog::create([
            'user_id' => $userId,
            'phone_number' => $phoneNumber,
            'message' => $message,
            'type' => $type,
            'status' => 'pending',
            'provider' => 'huduma',
        ]);

        try {
            // Format phone number for Tanzania (remove + sign for Huduma SMS)
            $formattedNumber = $this->formatPhoneNumberForHuduma($phoneNumber);

            $thirdPartyRef = 'fpct-' . $smsLog->id . '-' . uniqid();
            $payload = [
                'senderId' => $this->senderId,
                'envelopes' => [
                    [
                        'message' => $message,
                        'number' => $formattedNumber,
                        'thirdPartyRef' => $thirdPartyRef
                    ]
                ]
            ];

            if ($this->callbackUrl) {
                $payload['callbackUrl'] = $this->callbackUrl;
            }

            $response = $this->httpClient->post('/api/v3/sms/send', [
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Update SMS log with third party reference
            $smsLog->update(['third_party_ref' => $thirdPartyRef]);

            // Check if the SMS was sent successfully (202 Accepted)
            if ($response->getStatusCode() === 202 && isset($responseData['data']['outgoings'])) {
                $outgoings = $responseData['data']['outgoings'];
                foreach ($outgoings as $outgoing) {
                    // Huduma SMS uses 'pending' status for queued messages
                    if (in_array($outgoing['status'], ['QUEUED', 'pending', 'PENDING'])) {
                        $smsLog->markAsSent(
                            $outgoing['smscId'] ?? $outgoing['id'] ?? null,
                            $responseData
                        );
                        $smsLog->update(['credits_used' => $outgoing['credits'] ?? 1]);

                        Log::info("SMS sent successfully to {$formattedNumber} via Huduma SMS (Status: {$outgoing['status']})");
                        return true;
                    }
                }
            }

            $smsLog->markAsFailed('SMS sending failed: ' . json_encode($responseData));
            Log::warning("SMS sending failed for {$formattedNumber}. Response: " . json_encode($responseData));
            return false;

        } catch (RequestException $e) {
            $smsLog->markAsFailed('API error: ' . $e->getMessage());
            Log::error("Huduma SMS API error: " . $e->getMessage());

            // In local development, simulate success for testing
            if (config('app.env') === 'local') {
                $smsLog->markAsSent(null, ['simulated' => true]);
                Log::warning("Huduma SMS: Local development mode. Message would be: {$message} to {$phoneNumber}");
                return true; // Simulate success for local development
            }

            return false;
        } catch (\Exception $e) {
            $smsLog->markAsFailed('General error: ' . $e->getMessage());
            Log::error("Huduma SMS general error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send SMS to multiple recipients
     */
    public function sendBulkSMS(array $phoneNumbers, string $message): array
    {
        $results = [];

        try {
            // Create envelopes for each phone number
            $envelopes = [];
            foreach ($phoneNumbers as $phoneNumber) {
                $formattedNumber = $this->formatPhoneNumberForHuduma($phoneNumber);
                $envelopes[] = [
                    'message' => $message,
                    'number' => $formattedNumber,
                    'thirdPartyRef' => 'fpct-bulk-' . uniqid()
                ];
            }

            $payload = [
                'senderId' => $this->senderId,
                'envelopes' => $envelopes
            ];

            if ($this->callbackUrl) {
                $payload['callbackUrl'] = $this->callbackUrl;
            }

            $response = $this->httpClient->post('/api/v3/sms/send', [
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            // Process results for each recipient
            if ($response->getStatusCode() === 202 && isset($responseData['data']['outgoings'])) {
                foreach ($responseData['data']['outgoings'] as $outgoing) {
                    $phoneNumber = '+' . $outgoing['recipient']; // Add + back for consistency
                    $status = $outgoing['status'];
                    $credits = $outgoing['credits'] ?? 'N/A';

                    $results[$phoneNumber] = [
                        'status' => $status,
                        'cost' => $credits,
                        'success' => $status === 'QUEUED',
                        'sms_id' => $outgoing['smscId'] ?? null
                    ];

                    if ($status === 'QUEUED') {
                        Log::info("Bulk SMS sent successfully to {$phoneNumber} via Huduma SMS");
                    } else {
                        Log::error("Failed to send bulk SMS to {$phoneNumber}: {$status}");
                    }
                }
            }

        } catch (RequestException $e) {
            Log::error("Huduma SMS bulk API error: " . $e->getMessage());

            // Return failed status for all numbers
            foreach ($phoneNumbers as $number) {
                $results[$number] = [
                    'status' => 'Failed',
                    'cost' => 'N/A',
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        } catch (\Exception $e) {
            Log::error("Huduma SMS bulk general error: " . $e->getMessage());

            // Return failed status for all numbers
            foreach ($phoneNumbers as $number) {
                $results[$number] = [
                    'status' => 'Failed',
                    'cost' => 'N/A',
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Send premium SMS (for important notifications)
     * Note: Huduma SMS doesn't have separate premium SMS, so this uses regular SMS with callback
     */
    public function sendPremiumSMS(string $phoneNumber, string $message, string $keyword = null): bool
    {
        try {
            $formattedNumber = $this->formatPhoneNumberForHuduma($phoneNumber);

            $payload = [
                'senderId' => $this->senderId,
                'envelopes' => [
                    [
                        'message' => $message,
                        'number' => $formattedNumber,
                        'thirdPartyRef' => 'fpct-premium-' . uniqid()
                    ]
                ],
                'callbackUrl' => $this->callbackUrl // Always use callback for premium SMS
            ];

            $response = $this->httpClient->post('/api/v3/sms/send', [
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 202 && isset($responseData['data']['outgoings'])) {
                $outgoings = $responseData['data']['outgoings'];
                foreach ($outgoings as $outgoing) {
                    if ($outgoing['status'] === 'QUEUED') {
                        Log::info("Premium SMS sent successfully to {$formattedNumber} via Huduma SMS");
                        return true;
                    }
                }
            }

            Log::warning("Premium SMS sending failed for {$formattedNumber}. Response: " . json_encode($responseData));
            return false;

        } catch (RequestException $e) {
            Log::error("Huduma SMS premium API error: " . $e->getMessage());
            return false;
        } catch (\Exception $e) {
            Log::error("Huduma SMS premium general error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get SMS delivery reports
     */
    public function getDeliveryReports(int $page = 1, int $records = 10): array
    {
        try {
            $response = $this->httpClient->get('/api/v3/sms', [
                'query' => [
                    'page' => $page,
                    'records' => $records,
                    'sort' => ['id', 'desc']
                ]
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 200 && isset($responseData['data']['content'])) {
                return $responseData['data']['content'];
            }

            return [];
        } catch (RequestException $e) {
            Log::error("Failed to fetch SMS delivery reports from Huduma SMS: " . $e->getMessage());
            return [];
        } catch (\Exception $e) {
            Log::error("Failed to fetch SMS delivery reports: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get account balance
     * Note: Huduma SMS doesn't have a separate balance endpoint,
     * but balance is returned with each SMS send response
     */
    public function getBalance(): ?string
    {
        try {
            // Send a test request to get balance from response
            // We'll send to a dummy number to get the balance without actually sending
            $payload = [
                'senderId' => $this->senderId,
                'envelopes' => [
                    [
                        'message' => 'Balance check',
                        'number' => '************', // Dummy number for balance check
                        'thirdPartyRef' => 'balance-check-' . uniqid()
                    ]
                ]
            ];

            $response = $this->httpClient->post('/api/v3/sms/send', [
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() === 202 && isset($responseData['data']['balance'])) {
                return (string) $responseData['data']['balance'];
            }

            return null;
        } catch (RequestException $e) {
            Log::error("Failed to fetch Huduma SMS balance: " . $e->getMessage());
            return null;
        } catch (\Exception $e) {
            Log::error("Failed to fetch balance: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Format phone number for Tanzania
     * Converts various formats to +255XXXXXXXXX (for display/validation)
     */
    private function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);

        // Handle different formats
        if (str_starts_with($cleaned, '+255')) {
            // Already in correct format
            return $cleaned;
        } elseif (str_starts_with($cleaned, '255')) {
            // Add + prefix
            return '+' . $cleaned;
        } elseif (str_starts_with($cleaned, '0')) {
            // Replace leading 0 with +255
            return '+255' . substr($cleaned, 1);
        } elseif (strlen($cleaned) === 9) {
            // Assume it's a local number without country code
            return '+255' . $cleaned;
        } else {
            // Return as is
            return $cleaned;
        }
    }

    /**
     * Format phone number for Huduma SMS API
     * Converts various formats to 255XXXXXXXXX (without + sign)
     */
    private function formatPhoneNumberForHuduma(string $phoneNumber): string
    {
        // First format to standard +255 format
        $standardFormat = $this->formatPhoneNumber($phoneNumber);

        // Remove the + sign for Huduma SMS API
        return ltrim($standardFormat, '+');
    }

    /**
     * Validate phone number format
     */
    public function isValidPhoneNumber(string $phoneNumber): bool
    {
        $formatted = $this->formatPhoneNumber($phoneNumber);
        
        // Tanzania phone numbers should be +255 followed by 9 digits
        return preg_match('/^\+255\d{9}$/', $formatted);
    }

    /**
     * Get SMS pricing information
     */
    public function getSMSPricing(): array
    {
        // Tanzania SMS pricing (approximate - check with Africa's Talking for current rates)
        return [
            'local' => [
                'currency' => 'TZS',
                'price_per_sms' => 30, // Approximate price in Tanzanian Shillings
                'description' => 'Local SMS within Tanzania'
            ],
            'premium' => [
                'currency' => 'TZS',
                'price_per_sms' => 50,
                'description' => 'Premium SMS with delivery reports'
            ]
        ];
    }

    /**
     * Send OTP SMS with specific formatting
     */
    public function sendOTP(string $phoneNumber, string $otp, string $appName = 'FPCT', ?int $userId = null): bool
    {
        $message = "Your {$appName} verification code is: {$otp}. This code will expire in 30 minutes. Do not share this code with anyone.";

        return $this->sendSMS($phoneNumber, $message, 'otp', $userId);
    }

    /**
     * Send welcome SMS for new users
     */
    public function sendWelcomeSMS(string $phoneNumber, string $userName, string $username, string $tempPassword, string $otp, ?int $userId = null): bool
    {
        $message = "Welcome to FPCT system, Email: {$username}, Password: {$tempPassword}, OTP: {$otp}. Please login and change your password immediately.";

        return $this->sendSMS($phoneNumber, $message, 'welcome', $userId);
    }

    /**
     * Send announcement SMS
     */
    public function sendAnnouncementSMS(string $phoneNumber, string $title, string $content, ?int $userId = null): bool
    {
        $message = "FPCT Announcement: {$title}\n\n{$content}";

        // Truncate if too long (SMS limit is usually 160 characters for single SMS)
        if (strlen($message) > 160) {
            $message = substr($message, 0, 157) . '...';
        }

        return $this->sendSMS($phoneNumber, $message, 'announcement', $userId);
    }

    /**
     * Send transaction completion SMS
     */
    public function sendTransactionSMS(
        string $phoneNumber,
        string $transactionType,
        float $amount,
        string $currency,
        string $fromChurch,
        string $toChurch,
        string $purpose,
        string $transactionId,
        string $referenceNumber,
        ?int $userId = null
    ): bool {
        $formattedAmount = number_format($amount, 2);

        $message = "FPCT Transaction Completed!\n";
        $message .= "Type: {$transactionType}\n";
        $message .= "Amount: {$currency} {$formattedAmount}\n";
        $message .= "From: {$fromChurch}\n";
        $message .= "To: {$toChurch}\n";
        $message .= "Purpose: {$purpose}\n";
        $message .= "Ref: {$referenceNumber}\n";
        $message .= "ID: {$transactionId}";

        return $this->sendSMS($phoneNumber, $message, 'transaction', $userId);
    }

    /**
     * Send password reset SMS with OTP
     */
    public function sendPasswordResetSMS(string $phoneNumber, string $username, string $newPassword, string $otp, ?int $userId = null): bool
    {
        $message = "FPCT Password Reset - Username: {$username}, New Password: {$newPassword}, OTP: {$otp}. Please login and change your password immediately for security.";
        return $this->sendSMS($phoneNumber, $message, 'password_reset', $userId);
    }

    /**
     * Send password reset code SMS
     */
    public function sendPasswordResetCodeSMS(string $phoneNumber, string $username, string $resetCode, ?int $userId = null): bool
    {
        $message = "FPCT Password Reset Code - Username: {$username}, Reset Code: {$resetCode}. Use this code to reset your password. Code expires in 30 minutes.";
        return $this->sendSMS($phoneNumber, $message, 'password_reset', $userId);
    }

    /**
     * Test SMS service connectivity
     */
    public function testConnection(): bool
    {
        try {
            // For local development, we'll assume connection is working if credentials are set
            if (config('app.env') === 'local') {
                $apiToken = config('services.hudumasms.api_token');
                $senderId = config('services.hudumasms.sender_id');

                if (!empty($apiToken) && !empty($senderId)) {
                    Log::info("Huduma SMS: Using local development mode, credentials configured");
                    return true;
                }
                return false;
            }

            // Test connection by trying to get message history
            $response = $this->httpClient->get('/api/v3/sms', [
                'query' => ['records' => 1, 'page' => 1]
            ]);

            return $response->getStatusCode() === 200;
        } catch (RequestException $e) {
            Log::error("Huduma SMS connection test failed: " . $e->getMessage());

            // In local development, if it's an SSL error, we'll still consider it "connected"
            if (config('app.env') === 'local' && str_contains($e->getMessage(), 'SSL certificate')) {
                Log::info("Huduma SMS: SSL error in local development, but credentials appear valid");
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error("Huduma SMS connection test failed: " . $e->getMessage());
            return false;
        }
    }
}

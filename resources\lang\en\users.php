<?php

return [
    // Page titles
    'index_title' => 'Users - FPCT System',
    'create_title' => 'Add New User - FPCT System',
    'edit_title' => 'Edit User - FPCT System',
    'show_title' => 'User Details - FPCT System',
    'profile_title' => 'User Profile - FPCT System',
    'first_login_title' => 'Complete Profile - FPCT System',
    
    // Page headers
    'user_management' => 'User Management',
    'add_new_user' => 'Add New User',
    'edit_user' => 'Edit User',
    'user_details' => 'User Details',
    'user_profile' => 'User Profile',
    'complete_profile' => 'Complete Your Profile',
    'manage_users_system' => 'Manage users in the FPCT system',
    
    // Form fields
    'full_name' => 'Full Name',
    'email_address' => 'Email Address',
    'phone_number' => 'Phone Number',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'church_assignment' => 'Church Assignment',
    'user_role' => 'User Role',
    'profile_picture' => 'Profile Picture',
    'date_of_birth' => 'Date of Birth',
    'gender' => 'Gender',
    'address' => 'Address',
    'bio' => 'Biography',
    'locale' => 'Language',
    'emergency_contact_name' => 'Emergency Contact Name',
    'emergency_contact_phone' => 'Emergency Contact Phone',
    'is_active' => 'Is Active',
    
    // Placeholders
    'enter_full_name' => 'Enter full name',
    'enter_email' => 'Enter email address',
    'enter_phone' => 'Enter phone number',
    'enter_password' => 'Enter password',
    'select_church' => 'Select Church',
    'select_role' => 'Select Role',
    'select_gender' => 'Select Gender',
    'enter_address' => 'Enter address',
    'enter_bio' => 'Tell us about yourself',
    'enter_emergency_name' => 'Enter emergency contact name',
    'enter_emergency_phone' => 'Enter emergency contact phone',
    
    // Gender options
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',
    'prefer_not_to_say' => 'Prefer not to say',
    
    // Actions
    'add_user' => 'Add User',
    'save_user' => 'Save User',
    'update_user' => 'Update User',
    'delete_user' => 'Delete User',
    'activate_user' => 'Activate User',
    'deactivate_user' => 'Deactivate User',
    'reset_password' => 'Reset Password',
    'view_profile' => 'View Profile',
    'edit_profile' => 'Edit Profile',
    'change_password' => 'Change Password',
    
    // Table headers
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'church' => 'Church',
    'role' => 'Role',
    'status' => 'Status',
    'last_login' => 'Last Login',
    'actions' => 'Actions',
    'joined' => 'Joined',
    
    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'first_login_pending' => 'First Login Pending',
    'never_logged_in' => 'Never logged in',
    'online' => 'Online',
    'offline' => 'Offline',
    
    // Statistics
    'total_users' => 'Total Users',
    'active_users' => 'Active Users',
    'inactive_users' => 'Inactive Users',
    'pending_first_login' => 'Pending First Login',
    'users_by_role' => 'Users by Role',
    'users_by_church' => 'Users by Church',
    'recently_joined' => 'Recently Joined',
    
    // Messages
    'user_created' => 'User created successfully',
    'user_updated' => 'User updated successfully',
    'user_deleted' => 'User deleted successfully',
    'user_activated' => 'User activated successfully',
    'user_deactivated' => 'User deactivated successfully',
    'password_reset' => 'Password reset successfully',
    'profile_updated' => 'Profile updated successfully',
    'password_changed' => 'Password changed successfully',
    'no_users_found' => 'No users found',
    'profile_completion_required' => 'Please complete your profile information to continue using the system.',
    
    // Confirmations
    'confirm_delete' => 'Are you sure you want to delete this user?',
    'confirm_activate' => 'Are you sure you want to activate this user?',
    'confirm_deactivate' => 'Are you sure you want to deactivate this user?',
    'confirm_reset_password' => 'Are you sure you want to reset this user\'s password?',
    'delete_warning' => 'This will permanently delete the user and all associated data.',
    
    // Filters
    'all_users' => 'All Users',
    'filter_by_role' => 'Filter by Role',
    'filter_by_church' => 'Filter by Church',
    'filter_by_status' => 'Filter by Status',
    'search_users' => 'Search by name, email, or phone...',
    'show_active_only' => 'Show Active Only',
    'show_inactive_only' => 'Show Inactive Only',
    
    // Profile sections
    'personal_information' => 'Personal Information',
    'contact_information' => 'Contact Information',
    'church_information' => 'Church Information',
    'church_role_information' => 'Church & Role Information',
    'emergency_contact' => 'Emergency Contact',
    'account_settings' => 'Account Settings',
    'security_settings' => 'Security Settings',
    'user_information' => 'User Information',
    'editing_user' => 'Editing User',
    'permissions' => 'Permissions',
    'account_created' => 'Account Created',
    'last_updated' => 'Last Updated',
    'active_user' => 'Active User',
    'active_user_description' => 'User can log in and access the system',
    'update_user' => 'Update User',
    
    // Validation
    'name_required' => 'Full name is required',
    'email_required' => 'Email address is required',
    'email_unique' => 'This email address is already taken',
    'phone_required' => 'Phone number is required',
    'phone_unique' => 'This phone number is already taken',
    'password_required' => 'Password is required',
    'password_min' => 'Password must be at least 8 characters',
    'password_confirmation' => 'Password confirmation does not match',
    'church_required' => 'Church assignment is required',
    'role_required' => 'User role is required',
    'invalid_email' => 'Please enter a valid email address',
    'invalid_phone' => 'Please enter a valid phone number',
    
    // Breadcrumbs
    'users' => 'Users',
    'add' => 'Add',
    'edit_breadcrumb' => 'Edit',
    'profile' => 'Profile',
    'first_login' => 'First Login',
];

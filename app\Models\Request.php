<?php

namespace App\Models;

use App\Enums\ChurchLevel;
use Illuminate\Database\Eloquent\Model;

class Request extends Model
{
    protected $fillable = [
        'church_id', 'user_id', 'type', 'status', 'approved_by', 'approved_at', 'details',
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'details' => 'array',
    ];

    public function church()
    {
        return $this->belongsTo(Church::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function approvalWorkflows()
    {
        return $this->hasMany(ApprovalWorkflow::class)->orderBy('step_order');
    }

    public function currentWorkflowStep()
    {
        return $this->approvalWorkflows()->where('status', 'pending')->first();
    }

    /**
     * Check if request meets financial requirements for approval
     */
    public function meetsFinancialRequirements(): bool
    {
        // Only check financial requirements for upgrade requests
        if (!str_contains($this->type, 'upgrade')) {
            return true;
        }

        return $this->church->hasMinimumBalanceForUpgrade();
    }

    /**
     * Get financial requirement details
     */
    public function getFinancialRequirementDetails(): array
    {
        if (!str_contains($this->type, 'upgrade')) {
            return [
                'required' => false,
                'message' => 'No financial requirements for this request type.'
            ];
        }

        $church = $this->church;
        $balance = $church->getFinancialBalance();
        $hasMinimum = $church->hasMinimumBalanceForUpgrade();

        $minimumBalances = [
            ChurchLevel::BRANCH->value => 500000, // 500,000 TZS
            ChurchLevel::PARISH->value => 1000000, // 1,000,000 TZS
            ChurchLevel::LOCAL->value => 2000000, // 2,000,000 TZS
            ChurchLevel::REGIONAL->value => 5000000, // 5,000,000 TZS
        ];

        $requiredBalance = $minimumBalances[$church->level->value] ?? 0;

        return [
            'required' => true,
            'has_minimum' => $hasMinimum,
            'current_balance' => $balance->available_balance,
            'required_balance' => $requiredBalance,
            'shortfall' => max(0, $requiredBalance - $balance->available_balance),
            'message' => $hasMinimum
                ? 'Financial requirements met for upgrade.'
                : 'Insufficient balance for upgrade. Required: ' . number_format($requiredBalance, 2) . ' TZS'
        ];
    }

    public function isFullyApproved(): bool
    {
        return $this->approvalWorkflows()->where('status', '!=', 'approved')->count() === 0;
    }

    public function isRejected(): bool
    {
        return $this->approvalWorkflows()->where('status', 'rejected')->exists();
    }

    /**
     * Check if a user can approve this request.
     */
    public function canBeApprovedBy(User $user): bool
    {
        // Only pending requests can be approved
        if ($this->status !== 'pending') {
            return false;
        }

        // User must have approve-requests permission
        if (!$user->hasPermissionTo('approve-requests')) {
            return false;
        }

        // Super Admin can approve any request
        if ($user->hasRole('Super Admin')) {
            return true;
        }

        $userChurch = $user->church()->first();
        $requestChurch = $this->church()->first();

        if (!$userChurch || !$requestChurch) {
            return false;
        }

        // For upgrade requests, use specific approval logic
        if (str_starts_with($this->type, 'upgrade_')) {
            return $this->canApproveUpgradeRequest($userChurch, $requestChurch);
        }

        // For other requests, use general approval logic
        return $userChurch->canApproveRequestsFor($requestChurch);
    }

    /**
     * Check if a church can approve upgrade requests for another church.
     */
    private function canApproveUpgradeRequest(Church $userChurch, Church $requestChurch): bool
    {
        $requiredLevel = match ($this->type) {
            'upgrade_branch_to_parish' => ChurchLevel::LOCAL,
            'upgrade_parish_to_local' => ChurchLevel::REGIONAL,
            'upgrade_local_to_regional' => ChurchLevel::NATIONAL,
            default => null,
        };

        if (!$requiredLevel) {
            return false;
        }

        // User's church must be at the required level and be an ancestor of the requesting church
        return $userChurch->level === $requiredLevel &&
               $userChurch->isAncestorOf($requestChurch);
    }
}

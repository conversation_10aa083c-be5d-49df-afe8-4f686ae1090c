<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('churches', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('level'); // Nation, Diocese, Local, Parish, Branch
            $table->string('location');
            $table->string('phone_number')->nullable();
            $table->text('address')->nullable();
            $table->string('email')->nullable();
            $table->string('district_region')->nullable(); // Mkoa/District region
            $table->date('date_established')->nullable();
            $table->foreignId('parent_church_id')->nullable()->constrained('churches')->onDelete('set null');

            // Financial Information
            $table->string('bank_name')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_name')->nullable();
            $table->string('mobile_money_number')->nullable();
            $table->string('mobile_money_provider')->nullable(); // Vodacom, Airtel, Tigo, Halotel
            $table->decimal('current_balance', 15, 2)->default(0.00);

            $table->softDeletes();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('churches');
    }
};

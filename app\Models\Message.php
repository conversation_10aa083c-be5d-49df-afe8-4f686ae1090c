<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Message extends Model
{
    protected $fillable = [
        'sender_id', 'content', 'church_id', 'is_group_message', 'is_announcement', 'priority',
    ];

    protected $casts = [
        'is_group_message' => 'boolean',
        'is_announcement' => 'boolean',
    ];

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function recipients()
    {
        return $this->belongsToMany(User::class, 'message_recipient', 'message_id', 'user_id')
                    ->withPivot('read_at')
                    ->withTimestamps();
    }

    public function church()
    {
        return $this->belongsTo(Church::class);
    }

    // Query Scopes for Performance
    public function scopeAnnouncements($query)
    {
        return $query->where('is_announcement', true);
    }

    public function scopeGroupMessages($query)
    {
        return $query->where('is_group_message', true);
    }

    public function scopeDirectMessages($query)
    {
        return $query->where('is_group_message', false);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where(function ($q) use ($user) {
            $q->where('sender_id', $user->id)
              ->orWhereHas('recipients', function ($subQ) use ($user) {
                  $subQ->where('user_id', $user->id);
              });
        });
    }

    public function scopeUnreadForUser($query, User $user)
    {
        return $query->whereHas('recipients', function ($q) use ($user) {
            $q->where('user_id', $user->id)
              ->whereNull('read_at');
        });
    }

    public function scopeWithSenderAndRecipients($query)
    {
        return $query->with(['sender:id,full_name,email', 'recipients:id,full_name,email']);
    }

    public function scopeOptimizedForListing($query)
    {
        return $query->select(['id', 'sender_id', 'content', 'is_group_message', 'is_announcement', 'priority', 'created_at'])
                    ->with(['sender:id,full_name']);
    }

    public function isUnreadForUser(User $user): bool
    {
        return $this->recipients()
                   ->where('user_id', $user->id)
                   ->whereNull('read_at')
                   ->exists();
    }
}

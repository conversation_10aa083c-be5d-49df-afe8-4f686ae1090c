<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management (Individual)
            'create-users',
            'edit-users',
            'delete-users',
            'view-users',
            'activate-users',
            'deactivate-users',

            // User Management (Grouped - for middleware)
            'manage-users',

            // Church Management (Individual)
            'create-churches',
            'edit-churches',
            'delete-churches',
            'view-churches',
            'manage-church-hierarchy',

            // Church Management (Grouped - for middleware)
            'manage-churches',

            // Request Management
            'create-requests',
            'view-requests',
            'approve-requests',
            'reject-requests',
            'manage-requests',

            // Messaging
            'send-messages',
            'view-messages',
            'send-bulk-messages',
            'send-announcements',
            'delete-messages',

            // Leadership
            'assign-leaders',
            'remove-leaders',
            'view-leaders',

            // Financial Management
            'view-financial-dashboard',
            'manage-contributions',
            'create-contributions',
            'view-contributions',
            'edit-contributions',
            'delete-contributions',
            'manage-transactions',
            'create-transactions',
            'view-transactions',
            'approve-transactions',
            'cancel-transactions',
            'view-financial-balance',
            'manage-financial-balance',
            'generate-receipts',
            'view-receipts',
            'generate-financial-reports',
            'view-financial-reports',
            'export-financial-reports',
            'manage-church-finances',
            'collect-revenue',
            'send-revenue',

            // System Administration
            'manage-system',
            'view-reports',
            'manage-permissions',
            'audit-logs',

            // Church Reports
            'view-church-reports',
            'generate-church-reports',
            'export-church-reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // National Level Roles
        $archbishop = Role::firstOrCreate(['name' => 'Archbishop']);
        $archbishop->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
            'create-churches', 'edit-churches', 'view-churches', 'manage-church-hierarchy', 'manage-churches',
            'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements', 'delete-messages',
            'assign-leaders', 'remove-leaders', 'view-leaders',
            'manage-system', 'view-reports', 'manage-permissions', 'audit-logs'
        ]);

        $nationalAssistant = Role::firstOrCreate(['name' => 'National Assistant']);
        $nationalAssistant->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'manage-users',
            'view-churches', 'edit-churches', 'manage-churches',
            'view-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-announcements',
            'view-leaders', 'view-reports'
        ]);

        $generalSecretary = Role::firstOrCreate(['name' => 'General Secretary']);
        $generalSecretary->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'manage-users',
            'create-churches', 'edit-churches', 'view-churches', 'manage-churches',
            'view-requests', 'approve-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements',
            'assign-leaders', 'view-leaders', 'view-reports'
        ]);

        $nationalTreasurer = Role::firstOrCreate(['name' => 'National Treasurer']);
        $nationalTreasurer->syncPermissions([
            'view-users', 'view-churches', 'view-requests',
            'send-messages', 'view-messages', 'view-reports',
            'view-financial-dashboard', 'manage-contributions', 'create-contributions', 'view-contributions',
            'edit-contributions', 'manage-transactions', 'view-transactions', 'approve-transactions',
            'view-financial-balance', 'manage-financial-balance', 'generate-receipts', 'view-receipts',
            'generate-financial-reports', 'view-financial-reports', 'export-financial-reports',
            'manage-church-finances', 'collect-revenue'
        ]);

        $nationalIT = Role::firstOrCreate(['name' => 'National IT']);
        $nationalIT->syncPermissions([
            'create-users', 'edit-users', 'delete-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
            'create-churches', 'edit-churches', 'delete-churches', 'view-churches', 'manage-church-hierarchy', 'manage-churches',
            'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements', 'delete-messages',
            'assign-leaders', 'remove-leaders', 'view-leaders',
            'manage-system', 'view-reports', 'manage-permissions', 'audit-logs',
            'view-church-reports', 'generate-church-reports', 'export-church-reports'
        ]);

        $nationalHR = Role::firstOrCreate(['name' => 'National HR']);
        $nationalHR->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
            'view-churches', 'view-requests',
            'send-messages', 'view-messages',
            'assign-leaders', 'remove-leaders', 'view-leaders', 'view-reports'
        ]);

        // Diocese Level Roles
        $bishop = Role::firstOrCreate(['name' => 'Bishop']);
        $bishop->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users',
            'create-churches', 'edit-churches', 'view-churches',
            'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements',
            'assign-leaders', 'remove-leaders', 'view-leaders', 'view-reports'
        ]);

        $dioceseSecretary = Role::firstOrCreate(['name' => 'Diocese Secretary']);
        $dioceseSecretary->syncPermissions([
            'create-users', 'edit-users', 'view-users',
            'view-churches', 'edit-churches',
            'view-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-announcements',
            'view-leaders', 'view-reports'
        ]);

        $dioceseTreasurer = Role::firstOrCreate(['name' => 'Diocese Treasurer']);
        $dioceseTreasurer->syncPermissions([
            'view-users', 'view-churches', 'view-requests',
            'send-messages', 'view-messages', 'view-reports',
            'view-financial-dashboard', 'manage-contributions', 'create-contributions', 'view-contributions',
            'edit-contributions', 'manage-transactions', 'view-transactions', 'approve-transactions',
            'view-financial-balance', 'manage-financial-balance', 'generate-receipts', 'view-receipts',
            'generate-financial-reports', 'view-financial-reports', 'export-financial-reports',
            'manage-church-finances', 'collect-revenue', 'send-revenue'
        ]);

        $dioceseIT = Role::firstOrCreate(['name' => 'Diocese IT']);
        $dioceseIT->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
            'create-churches', 'edit-churches', 'view-churches', 'manage-churches',
            'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-bulk-messages', 'send-announcements',
            'assign-leaders', 'remove-leaders', 'view-leaders',
            'view-reports', 'view-church-reports', 'generate-church-reports', 'export-church-reports'
        ]);

        // Local Level Roles
        $pastor = Role::firstOrCreate(['name' => 'Pastor']);
        $pastor->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users',
            'view-churches', 'edit-churches',
            'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-announcements',
            'assign-leaders', 'view-leaders', 'view-reports'
        ]);

        $localSecretary = Role::firstOrCreate(['name' => 'Local Secretary']);
        $localSecretary->syncPermissions([
            'create-users', 'edit-users', 'view-users',
            'view-churches', 'edit-churches',
            'view-requests', 'manage-requests',
            'send-messages', 'view-messages',
            'view-leaders', 'view-reports'
        ]);

        $localTreasurer = Role::firstOrCreate(['name' => 'Local Treasurer']);
        $localTreasurer->syncPermissions([
            'view-users', 'view-churches', 'view-requests',
            'send-messages', 'view-messages', 'view-reports',
            'view-financial-dashboard', 'manage-contributions', 'create-contributions', 'view-contributions',
            'edit-contributions', 'manage-transactions', 'view-transactions', 'approve-transactions',
            'view-financial-balance', 'manage-financial-balance', 'generate-receipts', 'view-receipts',
            'generate-financial-reports', 'view-financial-reports', 'export-financial-reports',
            'manage-church-finances', 'collect-revenue', 'send-revenue'
        ]);

        $localIT = Role::firstOrCreate(['name' => 'Local IT']);
        $localIT->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users', 'deactivate-users', 'manage-users',
            'create-churches', 'edit-churches', 'view-churches', 'manage-churches',
            'create-requests', 'view-requests', 'approve-requests', 'reject-requests', 'manage-requests',
            'send-messages', 'view-messages', 'send-announcements',
            'assign-leaders', 'remove-leaders', 'view-leaders',
            'view-reports', 'view-church-reports', 'generate-church-reports', 'export-church-reports'
        ]);

        // Parish Level Roles
        $parishPastor = Role::firstOrCreate(['name' => 'Parish Pastor']);
        $parishPastor->syncPermissions([
            'create-users', 'edit-users', 'view-users',
            'view-churches', 'edit-churches',
            'create-requests', 'view-requests',
            'send-messages', 'view-messages',
            'assign-leaders', 'view-leaders'
        ]);

        $parishSecretary = Role::firstOrCreate(['name' => 'Parish Secretary']);
        $parishSecretary->syncPermissions([
            'view-users', 'edit-users',
            'view-churches', 'edit-churches',
            'create-requests', 'view-requests',
            'send-messages', 'view-messages',
            'view-leaders'
        ]);

        $parishTreasurer = Role::firstOrCreate(['name' => 'Parish Treasurer']);
        $parishTreasurer->syncPermissions([
            'view-users', 'view-churches', 'view-requests',
            'send-messages', 'view-messages',
            'view-financial-dashboard', 'view-contributions', 'manage-transactions', 'view-transactions',
            'view-financial-balance', 'generate-receipts', 'view-receipts',
            'generate-financial-reports', 'view-financial-reports', 'export-financial-reports',
            'send-revenue'
        ]);

        $parishIT = Role::firstOrCreate(['name' => 'Parish IT']);
        $parishIT->syncPermissions([
            'create-users', 'edit-users', 'view-users', 'activate-users', 'manage-users',
            'view-churches', 'edit-churches', 'manage-churches',
            'create-requests', 'view-requests', 'manage-requests',
            'send-messages', 'view-messages',
            'assign-leaders', 'view-leaders',
            'view-church-reports', 'generate-church-reports', 'export-church-reports'
        ]);

        // Branch Level Roles
        $branchPastor = Role::firstOrCreate(['name' => 'Branch Pastor']);
        $branchPastor->syncPermissions([
            'view-users', 'edit-users',
            'view-churches', 'edit-churches',
            'create-requests', 'view-requests',
            'send-messages', 'view-messages',
            'view-leaders'
        ]);

        $branchSecretary = Role::firstOrCreate(['name' => 'Branch Secretary']);
        $branchSecretary->syncPermissions([
            'view-users', 'view-churches',
            'create-requests', 'view-requests',
            'send-messages', 'view-messages'
        ]);

        $branchTreasurer = Role::firstOrCreate(['name' => 'Branch Treasurer']);
        $branchTreasurer->syncPermissions([
            'view-users', 'view-churches', 'view-requests',
            'send-messages', 'view-messages',
            'view-financial-dashboard', 'view-contributions', 'view-transactions',
            'view-financial-balance', 'generate-receipts', 'view-receipts',
            'view-financial-reports', 'send-revenue'
        ]);

        $branchIT = Role::firstOrCreate(['name' => 'Branch IT']);
        $branchIT->syncPermissions([
            'view-users', 'edit-users',
            'view-churches', 'edit-churches',
            'create-requests', 'view-requests',
            'send-messages', 'view-messages',
            'view-church-reports', 'generate-church-reports', 'export-church-reports'
        ]);

        // Member Role
        $member = Role::firstOrCreate(['name' => 'Member']);
        $member->syncPermissions([
            'view-messages', 'send-messages'
        ]);
    }
}
